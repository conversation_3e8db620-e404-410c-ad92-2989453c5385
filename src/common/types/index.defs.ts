/** Generate by swagger-axios-codegen */
/* eslint-disable */
// @ts-nocheck
import axiosStatic, { AxiosInstance, AxiosRequestConfig } from 'axios';

export interface IRequestOptions extends AxiosRequestConfig {}

export interface IRequestConfig {
  method?: any;
  headers?: any;
  url?: any;
  data?: any;
  params?: any;
}

// Add options interface
export interface ServiceOptions {
  axios?: AxiosInstance;
}

// Add default options
export const serviceOptions: ServiceOptions = {};

// Instance selector
export function axios(configs: IRequestConfig, resolve: (p: any) => void, reject: (p: any) => void): Promise<any> {
  if (serviceOptions.axios) {
    return serviceOptions.axios
    .request(configs)
    .then(res => {
      resolve(res.data);
    })
    .catch(err => {
      reject(err);
    });
  } else {
    throw new Error('please inject yourself instance like axios  ');
  }
}

export function getConfigs(method: string, contentType: string, url: string, options: any): IRequestConfig {
  const configs: IRequestConfig = { ...options, method, url };
  configs.headers = {
    ...options.headers,
    'Content-Type': contentType
  };
  return configs;
}

export const basePath = '';

export interface IList<T> extends Array<T> {}
export interface List<T> extends Array<T> {}
export interface IDictionary<TValue> {
  [key: string]: TValue;
}
export interface Dictionary<TValue> extends IDictionary<TValue> {}

export interface IListResult<T> {
  items?: T[];
}

export class ListResultDto<T> implements IListResult<T> {
  items?: T[];
}

export interface IPagedResult<T> extends IListResult<T> {
  totalCount?: number;
  items?: T[];
}

export class PagedResultDto<T = any> implements IPagedResult<T> {
  totalCount?: number;
  items?: T[];
}

// customer definition
// empty

export interface ApplyDicomModel {
  /**  */
  dicomIds: number[];

  /**  */
  subjectId: number;
}

export interface ApplyDicomResModel {
  /**  */
  ln: string;

  /**  */
  uri: string;
}
export interface AuthorityModel {
  /** 权限名称,eg, plan:add */
  authority: string;

  /** 创建时间 */
  createdAt?: number;

  /** 删除时间 */
  deletedAt?: number;

  /** 显示名称, eg，添加治疗计划 */
  displayName: string;

  /** id */
  id: number;

  /** 修改时间 */
  updatedAt?: number;
}
export interface CoordinateModel {
  /**  */
  x: number;

  /**  */
  y: number;

  /**  */
  z: number;
}

export interface CopyPlanModel {
  /** 创建来源 */
  createSource: EnumPlanModelCreateSource;

  /** 数据名 */
  dataName?: string;

  /** 头模注册信息-注册矩阵 */
  instrumentMeta?: string;

  /** 方案备注名 */
  markName?: string;

  /** 头模注册信息-定标点 */
  markPoints?: MarkPointModel[];

  /** 头模注册信息-注册时间 */
  markPointsCreatedAt?: number;

  /** 原始确认的刺激方案 */
  snapshotConfirmedTreatmentTargets?: StimulateTargetModel[];

  /** 剩余治疗次数 */
  remainTreatmentCount?: number;

  /** 筛查信息 */
  screeningInfo?: ScreeningInfoModel;

  /** 预计治疗次数 */
  treatmentCount?: number;

  /** 刺激方案 */
  treatmentTargets?: StimulateTargetModel[];

  /** 方案类型，固定为4 */
  type?: number;
}

export interface CreateDeviceLogModel {
  /**  */
  comments?: string;

  /**  */
  deviceId: number;

  /**  */
  localTime: Date;

  /**  */
  type: EnumDeviceLogModelType;
}

export interface CreateDeviceModel {
  /**  */
  comments?: string;

  /**  */
  endpointId: string;

  /**  */
  endpointType: EnumDeviceModelEndpointType;

  /**  */
  hardwareId: string;

  /**  */
  modelCode: string;

  /**  */
  orgId: number;

  /**  */
  serialCode: string;

  /**  */
  status: EnumDeviceModelStatus;
}

export interface UpdateDeviceLocalPasswordModel{
  ids: number[];
  localPassword: string;
}
export interface CreateDicomModel {
  /**  */
  instanceNumber?: string;

  /**  */
  modality?: string;

  /**  */
  patientId?: string;

  /**  */
  payload?: DicomPayloadModel;

  /**  */
  seriesDate?: string;

  /**  */
  seriesNumber?: string;

  /**  */
  seriesTime?: string;

  /**  */
  storagePath: string;

  /**  */
  studyDate?: string;

  /**  */
  studyId?: string;

  /**  */
  studyTime?: string;
}

export interface CreatePlanLogModel {
  /**  */
  createdByEndpointId?: number;

  /**  */
  createdByUserId?: number;

  /**  */
  event: EnumCreatePlanLogModelEvent;

  /**  */
  happenAt: number;

  /**  */
  note: PlanLogEventNoteModel;

  /**  */
  treatmentId: string;
  uniqId: string;
  departmentId: number;
  subjectId: number;
  planId: number;
  orgId?: number;
  stimulusId?: string;
  stimulatedNumber?: number;
}

export interface CreatePlanLogsModel {
  /**  */
  logs: CreatePlanLogModel[];
}

export interface CreatePlanModel {
  /** 创建来源 */
  createSource: EnumCreatePlanModelCreateSource;

  /** 数据处理类型 */
  dataProcessingType: EnumCreatePlanModelDataProcessingType;

  /** 疾病类型 */
  diseaseType: DiseaseTypeEnum;

  /** plan 信息 */
  planInfo: PlanInfoModel;

  /** 数据包 ID */
  uploadId: number;
}
export interface CreateStimulusTemplateModel {
  /**  */
  name: string;

  /**  */
  stimulateTemplate: StimulateTemplateModel;
}

export type UpdateStimulusTemplateModel = StimulusTemplateModel & {id: number};

export interface CreateSubjectModel {
  /**  */
  comments?: string;

  createdByUserId?: number;

  /**  */
  conditionDescription?: string;

  /**  */
  dateOfBirth?: Date;

  /**  */
  departmentId: number;

  /**  */
  firstName: string;

  /**  */
  gender: number;

  /**  */
  lastName: string;

  /**  */
  mobile?: string;

  /**  */
  patientCode: string;

  /**  */
  pinyinFirstName: string;

  /**  */
  pinyinLastName: string;
}

export interface CreateUploadModel {
  /**  */
  orgId?: number;

  /**  */
  storagePath: string;

  /**  */
  subjectId?: number;

  businessType: any;

  happenAt?: any;

  fileName?: string;

  planId?: number;

}

export declare enum UploadBusinessType {
  Normal = 0,
  StimulusLog = 1
}

export interface DeleteDicomModel {
  /**  */
  dicomIds: number[];
}

export interface DeviceLogModel {
  /**  */
  comments?: string;

  /**  */
  createdAt?: Date;

  /**  */
  createdByUserId?: number;

  /**  */
  deviceId?: number;

  /**  */
  id?: number;

  /**  */
  localTime?: Date;

  /**  */
  type?: EnumDeviceLogModelType;
}



export interface DeviceModel {
  /**  */
  comments?: string;

  /**  */
  createdAt: Date;

  /**  */
  endpointId?: string;

  /**  */
  endpointType?: EnumDeviceModelEndpointType;

  /**  */
  hardwareId?: string;

  /**  */
  id?: number;

  /**  */
  modelCode?: string;

  /**  */
  orgId?: number;

  /**  */
  serialCode?: string;

  /**  */
  status?: EnumDeviceModelStatus;

  /**  */
  updatedAt: Date;
}


export interface DeviceListModel {
  /**  */
  Org?: OrgModel;

  /**  */
  accessCount?: number;

  /**  */
  accessFirstAt?: Date;

  /**  */
  accessLatestAt?: Date;

  /**  */
  comments?: string;

  /**  */
  createdAt: Date;

  /**  */
  endpointType?: EnumDeviceListModelEndpointType;

  /**  */
  hardwareId?: string;

  /**  */
  id: number;

  /**  */
  isPowerOn?: boolean;

  /**  */
  lastLoginDate?: Date;

  /**  */
  lastLoginUser?: string;

  /**  */
  modelCode?: string;

  /**  */
  orgId?: number;

  /**  */
  serialCode?: string;

  /**  */
  status?: EnumDeviceListModelStatus;

  /**  */
  updatedAt: Date;
}

export interface DicomModel {
  /**  */
  createdAt: Date;

  /**  */
  dcmType?: string;

  /**  */
  id?: number;

  /**  */
  instanceNumber?: string;

  /**  */
  modality?: string;

  /**  */
  orgId?: number;

  /**  */
  patientId?: string;

  /**  */
  payload?: DicomPayloadModel;

  /**  */
  seriesDate?: string;

  /**  */
  seriesDesc?: string;

  /**  */
  seriesNumber?: string;

  /**  */
  seriesTime?: string;

  /**  */
  status?: EnumDicomModelStatus;

  /**  */
  storagePath?: string;

  /**  */
  studyDate?: string;

  /**  */
  studyId?: string;

  /**  */
  studyTime?: string;

  /**  */
  updatedAt: Date;
}

export interface DicomPayloadModel {
  /**  */
  dcmType?: string;

  /**  */
  seriesDesc?: string;
}

export interface FileDownloadModel {
  /**  */
  signedUrl?: string;

  /**  */
  storagePath: string;
}

export interface ListPlanByConditionQueryModel {
  /**  */
  departmentIds?: number[];

  /**  */
  keyword?: string;

  /**  */
  orgIds?: number[];

  /**  */
  page: number;

  /**  */
  pageSize: number;

  /**  */
  sortFirstPlanIds?: number[];

  /**  */
  planStatus?: EnumPlanModelPlanStatus[];

  /**  */
  sortTypeEnum?: EnumListPlanByConditionQueryModelSortTypeEnum;
}

export interface ListPlanForSubjectQueryModel {
  /**  */
  planStatuses?: number[];
}

export interface ListStimulusTemplateQueryModel {
  /** 创建者 */
  createdByUserId?: number;

  /** 按名称查询 */
  keyword?: string;

  /** 分页-页数 */
  page?: number;

  /** 分页-每页数量 */
  pageSize?: number;

  /** 刺激类型 */
  stimulateType?: number;
}

export interface ListStimulusTemplateResultModel {
  /** 本页结果 */
  list: StimulusTemplateModel[];

  /** 结果总数 */
  total: number;
}

export interface ListSubjectByConditionQueryModel {
  /**  */
  departmentIds?: number[];

  /**  */
  keyword?: string;

  /**  */
  orgIds?: number[];

  /**  */
  page: number;

  /**  */
  pageSize: number;
}

export interface MarkPointAndMatrixModel {
  /**  */
  instrumentMeta?: string;

  /**  */
  markPoints?: MarkPointModel[];
}

export interface MarkPointModel {
  /**  */
  name?: string;

  /**  */
  ras?: CoordinateModel;

  /**  */
  surfRas?: CoordinateModel;

  /**  */
  vertex?: number;
}

export interface PageModelOfPlanModel {
  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: PlanModel[];

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 总数 */
  total: number;
}

export interface PageModelOfPntDepartmentModel {
  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: PntDepartmentModel[];

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 总数 */
  total: number;
}

export interface PageModelOfPntOrgModel {
  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records?: PntOrgModel[];

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 总数 */
  total?: number;
}

export interface PageModelOfPntRoleModel {
  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: PntRoleModel[];

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 总数 */
  total?: number;
}

export interface PageModelOfPntSubjectModel {
  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: PntSubjectModel[];

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 总数 */
  total?: number;
}

export interface PageModelOfPntUserModel {
  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: PntUserModel[];

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 总数 */
  total?: number;
}

export interface PageModelOfSubjectModel {
  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: SubjectModel[];

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 总数 */
  total: number;
}

export interface PlanInfoModel {
  /** 数据名 */
  dataName?: string;

  /** 头模注册信息-注册矩阵 */
  instrumentMeta?: string;

  /** 方案备注名 */
  markName?: string;

  /** 头模注册信息-定标点 */
  markPoints?: MarkPointModel[];

  /** 头模注册信息-注册时间 */
  markPointsCreatedAt?: number;

  /** 原始确认的刺激方案 */
  snapshotConfirmedTreatmentTargets?: StimulateTargetModel[];

  /** 剩余治疗次数 */
  remainTreatmentCount?: number;

  /** 筛查信息 */
  screeningInfo: ScreeningInfoModel;

  /** 预计治疗次数 */
  treatmentCount?: number;

  /** 预计治疗天数 */
  planned_treatment_days?: number;

  /** 已治疗天数 */
  completed_treatment_days?: number;

  /** 刺激方案 */
  treatmentTargets?: StimulateTargetModel[];

  /** 方案类型，固定为4 */
  type?: number;

  // 为true 标识云端审核
  isImmediateFromThirdParty?: boolean;

  // 原因
  requestAnotherReviewComment?: string;

  // 方案过期时间
  expireTime?: number;
}

export interface PlanLogEventNoteModel {
  /**  */
  costTime?: number;

  /**  */
  createPlanType?: number;

  /**  */
  event?: number;

  /**  */
  isSuccess?: boolean;

  /**  */
  target?: StimulateTargetModel;

  /**  */
  treatmentIndex?: number;

  operatorName?: string;
  instrumentModelId?: number;
  isAutoStop?: boolean;
  planInfo?: PlanInfoModel;
  motionThreshold?: number;
}

export interface PlanLogModel {
  InstrumentsModel: any;
  /**  */
  attenuationPercent?: number;

  /**  */
  createdAt?: number;

  /**  */
  createdByUser?: UserModel;

  /**  */
  createdByUserId?: number;
  happenAt: number;

  /**  */
  event: EnumCreatePlanLogModelEvent;

  /**  */
  id?: number;

  /**  */
  note?: PlanLogEventNoteModel;
  treatmentIndex?: number;
  treatmentId: string;
  uniqId: string;

  /**  */
  planId?: number;
  operatorName?: string;
  instrumentModelId?: number;
  isAutoStop?: boolean;
}

enum hasDemo {
  hasDemo = 1,
  hasNoDemo = 2,
};

export interface PlanModel {
  /** 确认方案者 */
  confirmedByUser?: UserModel;

  statusChangedAt?: number;

  /** 确认方案者id */
  confirmedByUserId?: number;

  /** 创建方式 */
  createSource: EnumPlanModelCreateSource;

  /** 创建时间 */
  createdAt: Date | number;

  /** 创建者 */
  createdByUser: UserModel;

  /** 创建者id */
  createdByUserId: number;

  /** 数据处理记录 */
  dataProcessings?: DataProcessingModel[];

  /** 所属科室 ID */
  departmentId: number;

  /** 疾病种类，抑郁症固定为2 */
  diseaseType: DiseaseTypeEnum;

  /** id */
  id: number;

  /** 最近治疗时间 */
  latestTreatmentAt?: Date;

  /** 是否需要审核 */
  needReview: number;

  /** 数据处理结果文件 */
  outputFiles?: OutputFile[];

  /** 父plan的id */
  pid?: number;

  /** 方案信息 */
  planInfo: PlanInfoModel;

  /** 审核记录 */
  planReviewRecord?: PlanReviewRecordModel;

  /** 方案状态 */
  planStatus: EnumPlanModelPlanStatus;

  /** 处理完成时间 */
  processingFinishedAt?: Date;

  /** 处理用时，毫秒 */
  processingTime?: number;

  /** 审核时间 */
  reviewedAt?: Date;

  /** 审核员id */
  reviewedByUserId?: number;

  /** 开始时间 */
  startedAt?: Date;

  /** 状态变更时间 */
  statusChangedAt?: Date;

  /** 所属患者 */
  subject: SubjectModel;

  /** 所属患者 ID */
  subjectId: number;

  /** 终止时间 */
  terminatedAt?: Date;

  /** 终止方案者 */
  terminatedByUser?: UserModel;

  /** 终止方案者id */
  terminatedByUserId?: number;

  /** 方案类型，固定为4 */
  type: number;

  /** 更新时间 */
  updatedAt: Date;

  errorInfos?: RunInfo[];
  updateLocationHappenAt?: number;
  updateLocationUserId?: number;
  productType: ProductLine;

  // 是否可以获取新方案
  canRequestAnotherReview?: boolean;

  // 获取新方案的同组方案数量
  anotherReviewGroupCount?: number;

  // 新增demo字段，判断是否为demo数据
  hasDemo?: hasDemo;

  // 判断是否为ptc下发
  hasPtc?: boolean;

  // ptcFileModal
  planFileModelList: PlanFileMode[];

  hasDeprecated?: boolean;
  /** 方案过期时间 */
  expireTime?: number;
};

export type PlanFileMode = {
  id: number;
  name: string;
  s3mainKey: string;
};

export type NewPlanModel = Omit<PlanModel, 'canRequestAnotherReview'> & {
  anotherReviewParentPlan: Omit<PlanModel, 'canRequestAnotherReview'>;
};

export interface PlanList{
  records: NewPlanModel[];
  current: number;
  size: number;
  number: number;
};

export interface RunInfo {
  errorCode	:string;
  filePath	:string;
  severity	:string;
}
export interface PlanReviewRecordModel {
  /** 备注 */
  comment?: string;

  /** id */
  id?: number;

  /** 是否通过 */
  isPassed?: number;

  /** orgId */
  orgId?: number;

  /** planId */
  planId?: number;

  /** 审核时间 */
  reviewedAt?: number;

  /** 审核者 */
  reviewedByUser?: UserModel;

  /** 审核者id */
  reviewedByUserId?: number;

  /** subjectId */
  subjectId?: number;
}

export interface PntAuthorityModel {
  /** 权限名称,eg, plan:add */
  authority?: string;

  /** 创建时间 */
  createdAt?: Date;

  /** 删除时间 */
  deletedAt?: Date;

  /** 显示名称, eg，添加治疗计划 */
  displayName: string;

  /** id */
  id: number;

  /** 修改时间 */
  updatedAt?: Date;
}

export interface PntChangePasswordModel {
  /** 新密码 */
  newPassword?: string;

  /** 密码 */
  password?: string;
}

export interface PntCreateRoleModel {
  /** 应用名称 */
  applicationName: string;

  /** 权限 id 列表 */
  authorityGroups?: number[];

  /** 角色显示名称 */
  displayName?: string;

  /** 角色所属机构 */
  orgId?: number;

  /** 角色名称，单应用下唯一 */
  roleName?: string;

  description?: string;
}

export interface PntCreateUserModel {
  /** departmentIds */
  departmentIds?: number[];

  /** 邮件 */
  email?: string;

  /** firstName */
  firstName?: string;

  /** 是否未冻结 */
  isActive?: number;

  /** isNGAdmin */
  isNGAdmin?: number;

  /** 是否是 RA 用户 */
  isNGRA?: number;

  /** isOrgAdmin */
  isOrgAdmin?: number;

  /** isReviewer */
  isReviewer?: number;

  /** lastName */
  lastName?: string;

  /** localUsername */
  localUsername?: string;

  /** orgId */
  orgId?: number;

  /** 密码 */
  password?: string;

  /** roleId */
  roleIds?: number[];

  /** 用户名 */
  username?: string;

  /** verifyStatus */
  verifyStatus?: number;
}
export interface PntUserWithPasswordModel {
  /** departmentIds */
  departmentIds?: number[];

  /** 邮件 */
  email?: string;

  /** firstName */
  firstName?: string;

  /** 是否未冻结 */
  isActive?: number;

  /** isNGAdmin */
  isNGAdmin?: number;

  /** 是否是 RA 用户 */
  isNGRA?: number;

  /** isOrgAdmin */
  isOrgAdmin?: number;

  /** isReviewer */
  isReviewer?: number;

  /** lastName */
  lastName?: string;

  /** localUsername */
  localUsername?: string;

  /** orgId */
  orgId?: number;

  /** 密码 */
  password: string;

  /** roleId */
  roleId?: number;

  /** 用户名 */
  username?: string;

  /** verifyStatus */
  verifyStatus?: number;
}

export interface PntDepartmentCreateModel {
  /** 描述 */
  description?: string;

  /** 科室名称 */
  name?: string;
}

export interface PntDepartmentModel {
  /** 创建时间 */
  createdAt?: Date;

  /** 创建者 id */
  createdByUserId?: number;

  /** 删除时间 */
  deleteAt?: Date;

  /** 描述 */
  description?: string;

  /** id */
  id: number;

  /** 科室名称 */
  name: string;

  /** 机构 */
  org?: PntOrgModel;

  /** 关联患者数 */
  subjectsCount?: number;

  /** 修改时间 */
  updatedAt?: Date;

  /** 更新者 id */
  updatedByUserId?: number;

  /** 关联用户数 */
  usersCount?: number;

  // 是否为预制数据
  hasDemo?: hasDemo;

  //是否为ptc下发
  initType?: number;
}

export interface PntDepartmentUpdateModel {
  /** 描述 */
  description?: string;

  /** 科室名称 */
  name?: string;
}

export interface PntOrgCreateModel {
  /** contactEmail */
  contactEmail?: string;

  /** isActive */
  isActive?: number;

  /** isNGOrg */
  isNGOrg?: number;

  /** name */
  name?: string;

  /** needReview */
  needReview?: number;

  /** privacyProtect */
  privacyProtect?: number;

  /** rawConfig */
  rawConfig?: string;

  /** rawIpRules */
  rawIpRules?: string;

  /** uniqueId */
  uniqueId?: string;
}

export interface PntOrgModel {
  /** contactEmail */
  contactEmail?: string;

  /** 创建时间 */
  createdAt?: Date;

  /** 删除时间 */
  deleteAt?: Date;

  /** id */
  id?: number;

  /** isActive */
  isActive?: number;

  /** isNGOrg */
  isNGOrg?: number;

  /** name */
  name?: string;

  /** needReview */
  needReview?: number;

  /** privacyProtect */
  privacyProtect?: boolean;

  /** rawConfig */
  rawConfig?: string;

  /** rawIpRules */
  rawIpRules?: string;

  /** uniqueId */
  uniqueId?: string;

  /** 修改时间 */
  updatedAt?: Date;
}
export interface  PntOrgSummaryModel{

  orgModel: PntOrgModel;

  reviewerCount: number;

  orgUserCount: number;

  orgSubjectCount: number;

  treatmentCount:  number;

  treatmentDeviceCount : number;

  planProcessingCount : number;

  planComputeSuccessCount : number;

  planReviewedCount : number;

  planReviewFailedCount : number;

  planComputeFailedCount : number;
  dataProcessingCount: number;
  dataProcessingThisMonth: number;
  treatmentCountThisMonth: number;
}
export interface PntOrgUpdateModel{
  contactEmail?: string;

  isActive?: number;

  isNGOrg?: number;

  name?: string;

  needReview?: number;

  privacyProtect?: boolean;

  rawConfig	?: string;

  rawIpRules?: string;

  uniqueId?: string;
}
export interface PntOrgRAModel {
  /** 创建时间 */
  createdAt?: Date;

  /** 审核员 id */
  id?: number;

  /** orgId */
  orgId?: number;

  /** 修改时间 */
  updatedAt?: Date;

  /** userId */
  userId?: number;
}

export interface PntResetPasswordModel {
  /** 新密码 */
  newPassword: string;
}

export interface PntRoleModel {
  id: number;
  roleName: string;
  displayName: string;
  applicationName: string;
  authorityArr?: AuthorityModel[];
  authorities?: AuthorityModel[];
  description: string;
  createdAt?: number;
  createUser?: string;
  updatedAt?: number;
  updatedUser?: string;
  linkNum?: number;
  deleteAt?: number;
  deleteUser?: string;
}

export interface PntSubjectModel {
  /** 备注 */
  comments?: string;

  /** 病情描述 */
  conditionDescription?: string;

  /** 创建时间 */
  createdAt: Date;

  /** 创建者 */
  createdByUser?: PntUserModel;

  /** 创建者 id */
  createdByUserId?: number;

  /** 生日 */
  dateOfBirth: Date;

  /** 所属科室 */
  department?: PntDepartmentModel;

  /** 名字 */
  firstName: string;

  /** 性别 */
  gender: number;

  /** id */
  id: number;

  /** 姓氏 */
  lastName: string;

  /** 电话号码 */
  mobile?: string;

  /** 患者编号 */
  patientCode: string;

  /** pinyinFirstName */
  pinyinFirstName: string;

  /** pinyinLastName */
  pinyinLastName: string;

  /** 治疗方案数量 */
  planCount?: number;

  /** 已治疗次数 */
  usedTreatmentCount?: number;
}

export interface PntUpdateRoleModel {}

export interface PntUpdateUserModel {
  description:string;
  departmentIds: number[];
  firstName:	string;
  isActive:	boolean;
  lastName:	string;
  roleId: number;
}

export interface PntUserLoginModel {
  /** 用户名 */
  email?: string;

  /** 机构id */
  org?: string;

  /** 密码 */
  password?: string;
}

export interface PntUserLoginResponseModel {
  /** ipCheckResult */
  ipCheckResult?: number;

  /** isSubmission */
  isSubmission?: boolean;

  /** message */
  message?: string;

  /** platformType */
  platformType?: number;

  /** token */
  token?: string;

  /** user */
  user?: PntUserModel;
}

export interface PntUserModel {
  /** Org */
  Org?: PntOrgModel;

  /** OrgRAs */
  OrgRAs?: PntOrgRAModel[];

  departments: PntDepartmentModel[];
  /** 创建时间 */
  createdAt?: Date;

  /** 邮件 */
  email?: string;

  /** firstName */
  firstName?: string;

  /** 用户id */
  id: number;

  /** 是否未冻结 */
  isActive?: number;

  /** isNGAdmin */
  isNGAdmin?: number;

  /** 是否是 RA 用户 */
  isNGRA?: number;

  /** isOrgAdmin */
  isOrgAdmin?: number;

  /** isReviewer */
  isReviewer?: number;

  /** lastLoggedAt */
  lastLoggedAt?: Date;

  /** lastName */
  lastName?: string;

  /** localUsername */
  localUsername?: string;

  /** orgId */
  orgId?: number;

  /**  */
  password?: string;

  /** shouldChangePassword */
  shouldChangePassword?: boolean;

  /** 修改时间 */
  updatedAt?: Date;

  /** 用户名 */
  username?: string;

  /** verifyStatus */
  verifyStatus?: number;

  roles: PntRoleModel[];

  // 是否为ptc下发
  initType: number;
}

export interface ProcessInfoModel {}

export interface ScreeningInfoModel {
  /** 筛查-备注 */
  comment?: string;

  /** 筛查-疾病，目前固定100: 1; 200 :2 */
  disease: number;
  // 兼容100上的5中疾病的筛查
  screeningDiseaseArr?: ScreeningDiseaseEnum[];

  /** 筛查-自定义疾病名称 */
  diseaseName?: string;

  /** 筛查-癫痫 */
  hasEpilepsy?: boolean;

  /** 筛查-头部损伤 */
  hasHeadInjury?: boolean;

  /** 筛查-植入设备 */
  hasImplantedDevice?: boolean;

  /** 筛查-中风 */
  hasStroke?: boolean;

  /** 筛查-正在服药 */
  takingPsychotropicDrugs?: boolean;
}

export interface SetMarkNameModel {
  /**  */
  markName?: string;
}

export interface StimulateTargetModel {
  /**  */
  hemi: 'lh' | 'rh';

  /**  */
  isMotion: boolean;

  /**  */
  isUserAdd?: boolean;

  /**  */
  name?: string;

  originalConfirmedStimulateTemplate?: StimulateTemplateModel;
  /**  */
  parcId?: number;

  /**  */
  parcType?: number;

  /**  */
  selected?: boolean;

  /**  */
  stimulateTemplate?: StimulateTemplateModel;

  /**  */
  surfSeed: CoordinateModel;

  /**  */
  vertexIndex: number;

  /**  */
  vexelCoords?: CoordinateModel;

  /**  */
  volSeed: CoordinateModel;

  /**  */
  worldCoords?: CoordinateModel;

  type?: string;

  uuid?: string;
}

export interface StimulateTemplateModel {
  /** 串内脉冲数 */
  innerStrandPulseCount: number;

  /** 刺激间隔 */
  intermissionTime: number;

  /** 模板名字 */
  name?: string;

  id?: number;
  /** 刺激丛数 */
  plexusCount: number;

  /** 丛内频率 */
  plexusInnerFrequency: number;

  /** 丛内脉冲数 */
  plexusInnerPulseCount: number;

  /** 丛间频率 */
  plexusInterFrequency: number;

  /** 总脉冲数 */
  pulseTotal: number;

  /** 刺激类型 */
  stimulateType: StimulateType;

  /** 脉冲串数 */
  strandPulseCount: number;

  /** 串脉冲频率 */
  strandPulseFrequency: number;

  /** 刺激强度 */
  strength: number;

  /** 治疗时间 */
  treatmentTime: number;

  /** 刺激模版是否填写完成 */
  isNotComplete?: boolean;
}

export interface StimulusTemplateModel {
  /** 创建时间 */
  createdAt: Date;

  /** 创建者 */
  createdByUser: string;

  /** 创建者ID */
  createdByUserId?: number;

  /** 删除时间 */
  deletedAt?: Date;

  /** id */
  id?: number;

  /** 模板名字 */
  name?: string;

  /** 模板内容 */
  stimulateTemplate: StimulateTemplateModel;

  /** 刺激类型 */
  stimulateType: StimulateType;

  /** 更新时间 */
  updatedAt?: Date;
}

export interface StsCredentialModel {
  /**  */
  accessKeyId?: string;

  /**  */
  accessKeySecret?: string;

  /**  */
  expirationMS?: number;

  /**  */
  region?: string;

  /**  */
  resource?: string;

  /**  */
  securityToken?: string;

  endpoint?: string;
}

export interface RoleModel {
  /** 应用名称 */
  applicationName?: string;

  /** 角色列表 */
  authorities?: AuthorityModel[];

  /** 创建时间 */
  createdAt?: Date;

  /** 删除时间 */
  deletedAt?: Date;

  /** 角色显示名称 */
  displayName?: string;

  /** id */
  id?: number;

  /** 角色所属机构 */
  orgId?: number;

  /** 角色名称，单应用下唯一 */
  roleName?: string;

  /** 修改时间 */
  updatedAt?: Date;
}
export interface StsDownloadReqModel {
  /** 对象存储中的key */
  ossKey: string;
}

export interface StsUploadReqModel {
  /**  */
  fileName: string;

  /**  */
  uploadId?: string;
}

export interface SubjectModel {
  /** 备注 */
  comments?: string;

  /** 病情描述 */
  conditionDescription?: string;

  /** 创建时间 */
  createdAt: Date;

  /** 创建时间 */
  updatedAt: Date;

  /** 创建者 */
  createdByUser?: UserModel;

  createdByUserId?: number;

  /** 生日 */
  dateOfBirth: Date;

  /** 所属科室 */
  department?: PntDepartmentModel;

  /** 所属科室 ID */
  departmentId: number;

  /** 名字 */
  firstName: string;

  /** 性别 */
  gender: number;

  /** id */
  id: number;

  /** 姓氏 */
  lastName: string;

  /** 电话号码 */
  mobile?: string;

  /** orgId */
  orgId: number;

  /** 患者编号 */
  patientCode: string;

  /** pinyinFirstName */
  pinyinFirstName: string;

  /** pinyinLastName */
  pinyinLastName: string;

  /** 数据总结 */
  summary?: SubjectSummaryModel;

  // 运动阈值记录
  motionThreshold?: number;

  hasPtc?: boolean;

  hasDemo?: number;
}

export interface SubjectSummaryModel {
  /**  */
  mriCount?: number;

  /**  */
  planCount: number;

  /**  */
  treatCount: number;
}

export interface UpdateDeviceModel {
  /**  */
  comments?: string;

  /**  */
  endpointId?: string;

  localPassword?: string;
  deviceId?: number[];

  /**  */
  status?: EnumDeviceModelStatus;
}

export interface UpdateStimulateTemplateModel {
  name: string;
  hemi: 'lh' | 'rh';
  vertexIndex: number;
  stimulateTemplate: StimulateTemplateModel;
  uuid: string;
}

export interface UpdateStimulateTemplatesForPlanModel {
  /**  */
  stimulateTemplates?: StimulateTemplateModel[];
}

export interface UpdateSubjectModel {
  /**  */
  comments?: string;

  /**  */
  conditionDescription?: string;

  /**  */
  dateOfBirth?: Date;

  /** 所属科室 */
  departmentId?: number;

  /**  */
  firstName?: string;

  /**  */
  gender?: number;

  /**  */
  lastName?: string;

  /**  */
  mobile?: string;

  /**  */
  pinyinFirstName?: string;

  /**  */
  pinyinLastName?: string;
}

export interface UploadModel {
  /**  */
  createdAt: Date;

  /**  */
  deletedAt: Date;

  /**  */
  id?: number;

  /**  */
  orgId: number;

  /**  */
  status: EnumUploadModelStatus;

  /**  */
  storagePath: string;

  /**  */
  subjectId: number;

  /**  */
  updatedAt: Date;
}

export interface UserModel {
  /** 创建时间 */
  createdAt?: Date;

  /** 邮件 */
  email?: string;

  /** firstName */
  firstName?: string;

  /** 用户id */
  id: number;

  /** 是否未冻结 */
  isActive?: number;

  /** isNGAdmin */
  isNGAdmin?: number;

  /** 是否是 RA 用户 */
  isNGRA?: number;

  /** isOrgAdmin */
  isOrgAdmin?: number;

  /** isReviewer */
  isReviewer?: number;

  /** lastLoggedAt */
  lastLoggedAt?: Date;

  /** lastName */
  lastName?: string;

  /** localUsername */
  localUsername?: string;

  /** orgId */
  orgId?: number;

  /**  */
  password?: string;

  /** roles */
  roles?: RoleModel[];

  /** shouldChangePassword */
  shouldChangePassword?: number;

  /** 修改时间 */
  updatedAt?: Date;

  /** 用户名 */
  username: string;

  /** verifyStatus */
  verifyStatus?: number;
}

export interface OutputFile {
  /** 文件描述符 */
  descriptor: string;

  /** 文件ID */
  fileId: number;

  /** 文件逻辑名 */
  logicalName?: string;

  /** 文件key */
  s3MainKey: string;
}

export enum EnumPlanModelCreateSource {
  'LocalUpload' = 1,
  'PacsGain' = 2,
  'Reprocess' = 3,
  'Retreatment' = 4,
  'ModifyPlan' = 5,
  'ThirdParty' = 6,
  /** ptc下发 */
  'ptc' = 8,
  /** 手动倒入ng */
  'uploadNg' = 9,
}
export enum EnumCreatePlanModelDataProcessingType {
  'BOLD' = 3,
  'REQUEST_BOLD_RESULT' = 5,
  'T1' = 2,
  'T1_AND_BOLD' = 1,
  'T1_THEN_REQUEST_BOLD' = 4
}
export enum EnumCreatePlanModelDiseases {
  Empty = 1,
  Depression = 2,
  Aphasia = 3,
  MotorDeficits = 4
}
export enum EnumDeviceLogModelType {
  'Login' = 1,
  'Logout' = 2,
  'Poweroff' = 3,
  'Poweron' = 4
}

export enum EnumDeviceModelEndpointType {
  'Box' = 4,
  'Client' = 1,
  'Desktop' = 2,
  'Pacs' = 5,
  'Point' = 6,
  'PointRobot' = 7
}

export enum EnumDicomModelStatus {
  'Expired' = 4,
  'Processed' = 2,
  'Removed' = 3,
  'Uploaded' = 1
}

export enum EnumListPlanByConditionQueryModelSortTypeEnum {
  'CreatedAtAsc' = 1,
  'CreatedAtDesc' = 2,
  'ProcessingFinishedAtAsc' = 3,
  'ProcessingFinishedAtDesc' = 4
}

export enum EnumPlanModelDiseaseType {
  'Aphasia' = 1,
  'Depression' = 2,
  'Empty' = 3
}
export enum EnumPlanModelPlanStatus {
  'T1Computing'= 200010,
  'T1ComputeFail'= 200020,
  'T1ComputeTimeout'= 200030,
  'T1ComputeSuccess'= 200040,
  'BoldRequesting'= 200050,
  'BoldUploadFail'= 200060,
  'BoldComputeFail'= 200070,  // 计算和下载错误
  'BoldComputeTimeout'= 200080,
  'BoldComputeSuccess'= 200090,   // 待审核
  'BoldComputeReviewFailed'= 200100,
  'BoldComputeReviewed'= 200110,  // 审核通过
  'ProcessFinished'=200120,
  'NeedConfirm'= 200125,
  'Usable'= 200130,
  'Using'= 200140,
  'Terminated'= 200150,
  'Finished'= 200160,
  'Resting'=60,
}
export enum StimulateType {
  'Normal' = 1,
  'iTBS' = 2,
  'cTBS' = 3
}

export enum EnumDeviceModelStatus {
  'Active' = 10,
  'Banned' = 30,
  'InActive' = 20
}

export enum EnumUploadModelStatus {
  'Invalid' = 20,
  'Ready' = 10,
  'Valid' = 30
}
export enum EnumCreatePlanLogModelEvent {
  'CreatePlan' = 0,
  'CompleteDataProcessing' = 1,
  'StartTreatment' = 2,
  'StartStimulation' = 3,
  'StopStimulation' = 4,
  'StopTreatment' = 5,
  'TerminateTreatment' = 6,
  'CompleteBoldProcessing' = 7,
  'PlanInfoSaved' = 8,
  'PlanConfirmed' = 9,
}

// 头模注册信息-定标点
export interface MarkPointModel {
  name: string;
  ras: CoordinateModel;
  surfRas: CoordinateModel;
  vertex: number;
}


export interface UpdateCommonPlanInfoModel {
  instrumentMeta?: string; // 头模注册信息 - 注册矩阵
  markName?: string; // 方案备注名
  markPoints?: MarkPointModel[]; // 头模注册信息 - 定标点
  stimulateTemplatesToUpdate?: UpdateStimulateTemplateModel[]; // 更新刺激参数信息
  happenAt?: number;
  userId?: number;
}

export interface PlanTreatmentModel{
  hardwareId: string;
  planId: number;
  subjectId: number;
  userId:number;
  userName: string;
  serialCode: string;
}

export interface PlanFilesQueryModel{
  descriptors:string[];
}
export enum EnumImageViewDirection {
  'VFL_VFA_VFF' = 1,
  'VFR_VFP_VFH' = 2,
}
export interface PntUserPreferenceModel{
  agreeUserAgreement: boolean;
  imageViewDirection: string;
  userId: number;
}

export interface syncOfflineDataModel{
  syncId: string;
  syncType: number;
  stimulateTemplatesToUpdate?: UpdateStimulateTemplateModel[];
  motionThreshold?: number;
  happenAt: number;
  updateByUserId: number;
  planId?: number;
  subjectId?: number;
  orgId?: number;
}

export interface syncOfflineErrorModel {
  syncId: string;
  errorCode: string
}
export interface resSyncOfflineDataModel {
  successList: string[];
  errorList: syncOfflineErrorModel[];
}

export enum DiseaseTypeEnum {
  Empty = 1,
  Depression = 2,
  Aphasia = 3,
  MotorDeficits = 4
}

export enum ScreeningDiseaseEnum {
  Aphasia = 1,
  Depression = 2,
  Insomnia = 3,
  Hemiplegia = 4,
  Parkinson = 5
}

export enum ProductLine {
  point100 = 100,
  point200 = 200,
}

export enum PntAuthorityGroupEnum {
  NEW_SUBJECT =1,
  EDIT_SUBJECT =2,
  NEW_PLAN =3,
  EDIT_PLAN =4,
  CONFIRM_PLAN =5,
  DO_TREATMENT =6,
}

export enum TypeOfCTBS{
  RESEARCH = 1,
  CLINIC = 2,
}

export type DialogType = {
  content?: string;
  title?: string;
  okMessage?: string;
  cancelMessage?: string;
}

export enum Gender {
  Male = 1,
  Female = 2,
  other = 3,
}

export type DiskApi = {
  /** 目录 */
  fileSystem: string;
  /** 总容量 */
  sizeByte: string;
  /** 剩余大小 */
  freeByte: string;
  /** 使用百分比, 小数 */
  usedPercent: string;
}

/** 充放电次数api */
export type ChargeNumberApi = {
  stimulatedNumber: number;
}
