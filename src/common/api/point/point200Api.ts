import { IHttpClient } from '@/common/api/httpClient/IHttpClient';
import {
  CopyPlanModel,
  CreatePlanLogModel,
  CreateSubjectModel,
  CreateUploadModel,
  FileDownloadModel,
  ListPlanByConditionQueryModel,
  ListSubjectByConditionQueryModel,
  PageModelOfPlanModel,
  PageModelOfSubjectModel,
  PlanInfoModel,
  PlanModel,
  UpdateStimulateTemplateModel,
  StsCredentialModel,
  SubjectModel,
  UploadModel,
  CreatePlanModel,
  UpdateCommonPlanInfoModel,
  PlanTreatmentModel,
  PntOrgModel,
  PntOrgUpdateModel,
  PntOrgSummaryModel,
  PntDepartmentModel,
  PntUserPreferenceModel, syncOfflineDataModel, resSyncOfflineDataModel, PlanList,
  DiskApi,
  ChargeNumberApi,
} from '@/common/types/index.defs';
import { getEndpointInfo } from '@/common/lib/utils';
import { isKiosk } from '@/common/lib/env/env';
import { DeviceListModel, DeviceModel, StimulusLogDownloadModel, UpdateDeviceLocalPasswordModel } from '@device/pnt-component/common/types/index.def';
import { PreviewPlanApi } from '../../types/apiStatus';

export type RepoVersionDto = {
  applicationName: string;
  version?: string;
};
// 最后fetch走的 post get  是 api/baseAxiosHttpClientImps.ts文件
export class Point200Api {
  private httpClient: IHttpClient;

  constructor(httpClient: IHttpClient, authToken?: string, code?: string) {
    if (authToken) {
      httpClient.setAuthToken(authToken);
    }
    httpClient.setHeader('x-ng-techsupport-code', code);
    this.httpClient = httpClient;
  }

  // 返回同组的方案列表
  public async getAnotherReviewGroup(departmentId: number, subjectId: number, planId: number): Promise<PlanList> {
    return this.httpClient.restGetOne<{}, PlanList>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/anotherreviewgroup`,{});
  }

  // 获取新方案 接口
  public async getNewPlan(departmentId: number, subjectId: number, planId: number, param: { markName: string; requestAnotherReviewComment: string }): Promise<PlanModel> {
    return this.httpClient.restPost<{}, PlanModel>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/requestanotherreview`, param);
  }

  public async getPlanById(departmentId: number, subjectId: number, planId: number): Promise<PlanModel> {
    return this.httpClient.restGetOne<{}, PlanModel>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}`, {});
  }
  public async stopPlanById(projectId: number, subjectId: number, planId: number): Promise<any> {
    return this.httpClient.restPost<{}, any>(`/departments/${projectId}/subjects/${subjectId}/plans/${planId}/stop`, {});
  }

  // 获取科室详情
  public async getDepartment(id: number): Promise<PntDepartmentModel> {
    return this.httpClient.restGetOne<{}, PntDepartmentModel>(`/departments/${id}`, {});
  }

  public async getDepartmentList(condition: any): Promise<any> {
    // 获取科室列表  get
    return this.httpClient.restGet('/departments', condition);
  }
  public async createDepartment(body: any): Promise<any> {
    return this.httpClient.restPost('/api/departments/', body);
  }
  public async deleteDepartment(id: number): Promise<any> {
    // 删除单一科室  delete
    return this.httpClient.restDelete(`api/departments/${id}`);
  }
  public async updateDepartment(id: number, body: any): Promise<any> {
    return this.httpClient.restPut(`/api/departments/${id}`, body);
  }
  public async getRoleList(params: any): Promise<any> {
    // 获取角色列表 get
    return this.httpClient.restGet('/api/roles/', params);
  }
  public async createRole(body: any): Promise<any> {
    return this.httpClient.restPost('/api/roles/', body);
  }
  public async updateRole(id: number, body: any): Promise<any> {
    // 编辑角色 put
    return this.httpClient.restPut(`/api/roles/${id}`, body);
  }
  public async deleteRole(id: number): Promise<any> {
    // 删除单一角色  delete
    return this.httpClient.restDelete(`api/roles/${id}`);
  }
  public async getUserList(): Promise<any> {
    // 获取角色列表 get
    return this.httpClient.restGet('/api/users/', {});
  }
  public async createUser(body: any): Promise<any> {
    // 创建角色 post
    return this.httpClient.restPost('/api/roles/', body);
  }
  public async updateUser(id: number, body: any): Promise<any> {
    // 编辑角色 put
    return this.httpClient.restPut(`/api/roles/${id}`, body);
  }
  public async deleteUser(id: number): Promise<any> {
    // 删除单一角色  delete
    return this.httpClient.restDelete(`api/roles/${id}`);
  }

  public async getPlanListByPageCondition(condition: ListPlanByConditionQueryModel): Promise<PageModelOfPlanModel> {
    return this.httpClient.restPost<ListPlanByConditionQueryModel, PageModelOfPlanModel>('/plans/plansforproject', condition, 3, 10000);
  }
  public async checkExpire(ignorePlanIdList: number[] = []): Promise<number[]> {
    return this.httpClient.restPost<{ignorePlanIdList: number[]}, number[]>('/plans/checkExpire', {ignorePlanIdList}, 3, 10000);
  }

  public async getPlanListOfPatient(departmentId: number, subjectId: number): Promise<PageModelOfPlanModel> {
    return this.httpClient.restPost(`/departments/${departmentId}/subjects/${subjectId}/plans/list`, {page: -1, pageSize: -1}, 3, 10000);
  }

  public async getPatientListByCondition(condition: ListSubjectByConditionQueryModel): Promise<PageModelOfSubjectModel> {
    return this.httpClient.restPost<ListSubjectByConditionQueryModel, PageModelOfSubjectModel>('/subjects/listfororg', condition, 3, 10000);
  }

  public async createPatient(patientInfo: CreateSubjectModel): Promise<SubjectModel> {
    return this.httpClient.restPost<CreateSubjectModel, SubjectModel>('/subjects/', patientInfo,3, 10000);
  }

  public async updatePatient(id: number, patientInfo: SubjectModel): Promise<SubjectModel> {
    return this.httpClient.restPut<SubjectModel, SubjectModel>(`/subjects/${id}`, patientInfo);
  }

  public async updatePatientMotionScope(id: number, motionThreshold: number): Promise<SubjectModel> {
    return this.httpClient.restPost<{ }, SubjectModel>(`/subjects/${id}/motionthreshold`, { motionThreshold });
  }

  public async updatePatientMotionScopeOfOffline(id: number, motionThreshold: number, userId: number, happenAt: number): Promise<SubjectModel> {
    return this.httpClient.restPost<{ }, SubjectModel>(`/subjects/${id}/motionthreshold`, { motionThreshold, userId, happenAt });
  }

  public async updatePlanOfOffline(data: syncOfflineDataModel[]): Promise<resSyncOfflineDataModel> {
    return this.httpClient.restPost<syncOfflineDataModel[], resSyncOfflineDataModel>('plans/sync/offline/info', data);
  }

  public async getPatientByCode(patientCode: string): Promise<SubjectModel[]> {
    return this.httpClient.restGetOne<{}, SubjectModel[]>('/subjects/find', { patientCode: patientCode });
  }
  public async getPatient(patientId: number): Promise<SubjectModel> {
    return this.httpClient.restGetOne<{}, SubjectModel>(`/subjects/${patientId}`, {});
  }

  public async getDownloadFileUrl(fileList: FileDownloadModel[]): Promise<FileDownloadModel[]> {
    return this.httpClient.restPost<FileDownloadModel[], FileDownloadModel[]>('/file/downloadurls', fileList, 3, 60000);
  }

  public async updatePlanInfo(departmentId: number, subjectId: number, planId: number, planInfo: PlanInfoModel): Promise<PlanModel>{
    return this.httpClient.restPost<{}, PlanModel>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/saveinfo`, planInfo, 3, 10000);
  }

  public async confirmPlanInfo(departmentId: number, subjectId: number, planId: number, treatmentInfo: any): Promise<PlanModel>{
    return this.httpClient.restPost<{}, PlanModel>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/confirm`, treatmentInfo, 3, 10000);
  }

  public async copyPlanInfo(departmentId: number, subjectId: number, planId: number, planInfo: CopyPlanModel): Promise<PlanModel> {
    return this.httpClient.restPost<{}, PlanModel>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/copy`, planInfo, 3, 10000);
  }

  public async treatmentCountIncrease(projectId: number, subjectId: number, planId: number): Promise<any> {
    return this.httpClient.restPut<{}, any>(`/departments/${projectId}/subjects/${subjectId}/plans/${planId}/treatmentcountincrease`, {});
  }

  public async checkTreatment(departmentId: number, subjectId: number, planId: number): Promise<string> {
    return this.httpClient.restPost<{}, string>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/checktreatment`, {});
  }

  public async updateSpotListTemplate(projectId: number, subjectId: number,planId: number, templates: UpdateStimulateTemplateModel[]): Promise<any>{
    return this.httpClient.restPut<any, any>(`/departments/${projectId}/subjects/${subjectId}/plans/${planId}/updatestimulatetemplates`, templates);
  }

  public async getStsUploadToken(fileName: string): Promise<StsCredentialModel> {
    return this.httpClient.restPost<{}, StsCredentialModel>('/file/getStsUploadToken', {
      fileName,
    }, 3, 30000);
  }

  public async createUpload(uploadModel: CreateUploadModel): Promise<UploadModel> {
    return this.httpClient.restPost<CreateUploadModel, UploadModel>('/upload', uploadModel, 3, 540000);
  }

  public async createPlan(departmentId: number, subjectId: number, createPlan: CreatePlanModel): Promise<PlanModel> {
    return this.httpClient.restPost<CreatePlanModel, PlanModel>(`/departments/${departmentId}/subjects/${subjectId}/plans/`, createPlan, 3, 600000);
  }

  public async requestTarget(departmentId: number, subjectId: number, planId: number, uploadId: number, planInfo: PlanInfoModel) {
    return this.httpClient.restPost(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/requesttarget`, {
      uploadId,
      planInfo,
    }, 3, 600000);
  }

  public async fileDownloadUrls(departmentId: number, subjectId: number, planId: number, desc: string) {
    return this.httpClient.restPost(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/filedownloadurls`, {descriptors: [desc]});
  }

  public async updateCommonInfo(departmentId: number, subjectId: number, planId: number, commonPlanInfo: UpdateCommonPlanInfoModel) {
    return this.httpClient.restPost<UpdateCommonPlanInfoModel, PlanModel>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/updatecommoninfo`, commonPlanInfo);
  }

  public async reRunPlan(departmentId: number, subjectId: number, planId: number): Promise<any>{
    return this.httpClient.restPost<{}, any>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/rerun`, { });
  }

  public async setMarkName(departmentId: number, subjectId: number, planId: number, markName: string): Promise<any> {
    return this.httpClient.restPost<{}, any>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/updatecommoninfo`, { markName });
  }

  public async updatePlanInfoOfNative(departmentId: number, subjectId: number, planId: number, planInfoField: UpdateCommonPlanInfoModel): Promise<any> {
    return this.httpClient.restPost<{}, any>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/updatecommoninfo`, planInfoField);
  }

  public async createLogOfPlan(params: CreatePlanLogModel[]): Promise<any> {
    return this.httpClient.restPost<{}, any>('/plans/planlogs', { logs: params });
  }
  public async getLogsOfPlan(departmentId: number, subjectId: number, planId: number): Promise<any> {
    return this.httpClient.restGet<{}, any>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/planlogs/listall`, { });
  }

  // 心跳检测
  public async setHealthHeart(planIds: number[]): Promise<null>{
    return this.httpClient.restPost<{}, null>('/device/health', planIds, 0, 2000);
  }
  // 检查设备是否被其他设备治疗中
  public async getPlanTreatmentInfo(departmentId: number, subjectId: number, planId: number, userId: number): Promise<PlanTreatmentModel>{
    return this.httpClient.restPost<{}, PlanTreatmentModel>(`/departments/${departmentId}/subjects/${subjectId}/plans/${planId}/treatmentInfo`, {planId, subjectId, userId});
  }
  public async getPboxDisk(): Promise<any>{
    return this.httpClient.restGet<{},{}>('/disk/info',{});
  }

  // org
  public async getOrg(orgId: number, retryTimes?: number): Promise<PntOrgModel> {
    return this.httpClient.restGetOne<{}, PntOrgModel>(`/orgs/${orgId}`, {}, retryTimes);
  }

  public async updateOrgInfo(orgId: number, updateModal: PntOrgUpdateModel, retryTimes?: number): Promise<PntOrgModel> {
    return this.httpClient.restPut<PntOrgUpdateModel, PntOrgModel>(`/orgs/${orgId}`, updateModal, retryTimes);
  }

  public async getOrgSummary(orgId: number, retryTimes?: number): Promise<PntOrgSummaryModel & { initType?: number}> {
    return this.httpClient.restGetOne<{}, PntOrgSummaryModel>(`/orgs/${orgId}/summary`, {}, retryTimes);
  }

  public async checkCloudConfirm(orgId: number): Promise<any> {
    return this.httpClient.restPost(`/device/confirm/${orgId}`, {});
  }
  public async checkHavePasc(orgId: number): Promise<any> {
    return this.httpClient.restGet<{}, any>(`/device/haspacs/${orgId}`,{});
  }

  public async getUserPreference(): Promise<PntUserPreferenceModel> {
    return this.httpClient.restGetOne<{}, PntUserPreferenceModel>('/users/preference',{});
  }
  public async updateUserPreference(userPreference: PntUserPreferenceModel): Promise<any> {
    return this.httpClient.restPost<{}, any>('/users/preference', userPreference);
  }
  public async setDeviceLog(type: number): Promise<any>{
    if(!isKiosk()) return;
    let { hardwareId } = getEndpointInfo();

    return this.httpClient.restPost('/devicelog', {hardwareId, localTime: new Date().getTime(), type});
  }
  public async getPBoxDiskInfo(): Promise<any>{
    return this.httpClient.restGet('/disk/info',{type:'warn'});
  }

  public async getDiskInfo(): Promise<DiskApi[]>{
    return this.httpClient.restGet<{}, DiskApi>('/disk/v2/info', {});
  }

  public async getChargeNumber(): Promise<ChargeNumberApi>{
    return this.httpClient.restGetOne<{}, ChargeNumberApi>('plans/stimulatedNumber', {});
  }

  public async getSerialNumber(hardwareId: string): Promise<any>{
    return this.httpClient.restGetOne<{}, any>(`/device/hardwareId/${hardwareId}/serialNumber`, {});
  }

  public async putSerialNumber(hardwareId: string, serialNumber: string): Promise<any>{
    return this.httpClient.restPut<{}, any>(`/device/hardwareId/${hardwareId}/serialNumber/${serialNumber}`, { });
  }

  public async getQrCode(info: any): Promise<any>{
    return this.httpClient.restPost<{}, any>('/info/makeQrCode', info);
  }

  public async validateValidateQrCode(info: {code: string}): Promise<any>{
    return this.httpClient.restPost<{}, any>('/users/verify_techsupport_code', info);
  }
  public async getValidateQrCode(): Promise<any>{
    return this.httpClient.restPost<{}, any>('/users/request_techsupport_code', {});
  }

  public async getCastatus(): Promise<any>{
    return this.httpClient.restGet<{}, number>(
      '/ngfile/castatus', {}
    );
  }

  public async getRepoVersion(): Promise<RepoVersionDto[]> {
    return this.httpClient.restGet<{}, RepoVersionDto>('versions', {});
  }

  public async getStimulusLogs(startTime: number, endTime: number, timeout: number): Promise<StimulusLogDownloadModel> {
    return this.httpClient.restPost<{}, StimulusLogDownloadModel>('/stimulus/log/list', { startTime, endTime }, 0, timeout);
  }

  public async getDeviceList(orgId: number, retryTimes?: number): Promise<DeviceListModel[]> {
    return this.httpClient.restGet<{}, DeviceListModel>(`/device/org/${orgId}`, {}, retryTimes);
  }

  public async updateDeviceInfo(updateInfo: UpdateDeviceLocalPasswordModel, retryTimes?: number): Promise<DeviceModel[]> {
    return this.httpClient.restPut<UpdateDeviceLocalPasswordModel, DeviceModel[]>('/device/localpwd', updateInfo, retryTimes);
  }

  public async uploadNgFile(formData: FormData) {
    return this.httpClient.restPost<{}, any>('/ngfile/upload', formData, undefined, 1000 * 70);
  }

  public async previewPlan(key: string) {
    return this.httpClient.restPost<{}, PreviewPlanApi>(`/ngfile/preview/${key}`, {});
  }

  public async submitPreviewPlan(key: string, data: any) {
    return this.httpClient.restPost<{}, any>(`/ngfile/import/${key}`, data, undefined, 1000 * 70);
  }

  public async cancelTempPreview(key: string) {
    return this.httpClient.restPost<{}, any>(`/ngfile/cancel/${key}`, {});
  }
}
