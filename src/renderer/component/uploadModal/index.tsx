import React, { useEffect, useState } from 'react';
import { NgSelectFileModal } from '../NgSelectFileModal';
import { fileRootPath } from '../../utils/utils';
import { getFormData } from '../../pointUpload/pointUploadContainerUtils';
import { auth } from '../../store/configureStore';
import { getPoint200Api } from '../../../common/api/point/point200ApiUtils';
import { Modal, message } from 'antd';
import { handleGetFolderInfo } from '../NgFileList/utils';
import styles from './index.less';

type Iprops = {
  isShowFileModal: boolean;
  onCancel(): void;
  onOk(key: string): void;
};

export const UploadModal = (props: Iprops) => {
  const {isShowFileModal, onCancel, onOk} = props;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const jwtToken = auth.jwtToken();
  const fetchApi = getPoint200Api(jwtToken!);

  const handleFileSelected = async (path: string) => {
    try {
      setLoading(true);
      const formData = await getFormData(path);
      const res = await fetchApi.uploadNgFile(formData);
      onOk(res);
    } catch (error: any) {
      if(error?.status === 401) return;
      if (error?.status === 450) {
        if (error.code === 'PNT_PI10108') {
          Modal.warning({
            title: '方案已过期，无法创建治疗任务',
            okText: '我知道了',
            className: styles.modal_warning,
          });
        } else if (error.code === 'PNT_PI10109') {
          Modal.warning({
            title: '已存在该文件，无法再次导入',
            okText: '我知道了',
            className: styles.modal_warning,
          });
        } else {
          Modal.warning({
            title: <>文件信息不符合规范，请重新选择。<br />错误代码：{error.code}。</>,
            okText: '确定',
            className: styles.modal_warning,
          });
        }
      }else {
        Modal.warning({
          title: <>服务异常，请重启或联系技术支持。<br />错误代码:503。</>,
          okText: '确定',
          className: styles.modal_warning,
        });
      }
    } finally {
      setLoading(false);
      onCancel();
    }
  };

  const getHasUsb = async () => {
    try {
      const list = await handleGetFolderInfo(fileRootPath);
      if (list.length === 0) throw Error('未检测到移动设备');
      setVisible(true);
    } catch (error) {
      await message.error('未检测到移动设备');
      onCancel();
    }
  };

  useEffect(() => {
    if(isShowFileModal) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      getHasUsb();
    }else {
      setVisible(false);
    }
  }, [isShowFileModal]);

  return  <NgSelectFileModal
    filepath={fileRootPath}
    visible={isShowFileModal && visible}
    onCancel={onCancel}
    confirmLoading={loading}
    extName='.ng'
    onOk={(files) => {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      handleFileSelected(files[0].path);
    }}
  />;
};
