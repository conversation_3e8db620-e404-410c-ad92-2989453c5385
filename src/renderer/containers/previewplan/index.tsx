import React, { useEffect, useRef, useState } from 'react';
import { auth, history } from '../../store/configureStore';
import { getPoint200Api } from '../../../common/api/point/point200ApiUtils';
import { Button, FormInstance, Spin } from 'antd';
import styles from './index.less';
import { PreviewPlanApi } from '../../../common/types/apiStatus';
import { LoadingEnum, downFileUrl} from './config';
import { PointSurfaceViewer } from '../../component/pointSurfaceViewer/pointSurfaceViewer';
import { PointVolumeViewer } from '../../component/pointVolumeViewer/pointVolumeViewer';
import { Colormap } from '../../pnt-component/packages/ui-component/visualization/artiDef';
import { PatientForm } from './component/patientForm';
import { PreviewCommonSpot } from '../cureProjectContainer/components/previewCommonSpot';
import { SpotType } from '../../utils/pageType';
import { initSpotList, removerDot, updateSurfaceCursorByWord, updateVolumeCursorByWorld } from '../cureProjectContainer/utils/utils';
import { PreviewMotionSpot } from '../cureProjectContainer/components/previewMotionSpot';
import { StimulateTemplateModel } from '../../../common/types/index.defs';
import { UploadModal } from '../../component/uploadModal';
import { VolemeArtiDefMap, viewFileMap } from '../cureProjectContainer/utils/fileUtils';
import { QuarterLayout, QuarterSurface, QuarterVolumes } from '../../component/quarterLayout/quarterLayout';
import quarterLayoutStyle from '@/renderer/component/quarterLayout/quarterLayout.less';
import { usePrevious } from 'ahooks';
import classNames from 'classnames';
import { Coordinate } from '@ngiq/ngiq-api-types';
import moment from 'moment';
import { CommonObj } from '../../../common/lib/objUtils';
import { ValidateExpireModal } from '../../component/ValidateExpireModal/validateExpireModal';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const pinyin = require('pinyin');

type IProps = {
  match: {
    params: {
      previewId: string;
    };
  };
};

type PreviewSpotType = SpotType & {
  key: string;
  hasError: boolean;
};

export const PreviewPlan = (props: IProps) => {
  const [planInfo, setPlanInfo] = useState<PreviewPlanApi | null>(null);
  const [spotList, setSpotList] = useState<PreviewSpotType[]>([]);
  const [loadStatus, setLoadStatus] = useState(LoadingEnum.init);
  const [isShowFileModal, setIsShowFileModal] = useState(false);
  const [previewId, setPreviewId] = useState<string>(props.match.params.previewId);
  const previousId = usePrevious(previewId);
  // const [sc]
  const jwtToken = auth.jwtToken();
  const fetchApi = getPoint200Api(jwtToken!);
  const fileInfoRef = useRef<CommonObj<string>>({});
  const surfaceViewerRef = useRef<any>(null);
  const volumeViewerRef = useRef<any>(null);
  const formRef = useRef<FormInstance>(null);
  const listRef = useRef<HTMLDivElement>(null);
  const loadStatusRef = useRef<LoadingEnum>(LoadingEnum.init);

  const getStrengthError = (stimulateTemplate?: Partial<StimulateTemplateModel>) => {
    return !!(stimulateTemplate && (stimulateTemplate.strength! < 1 || stimulateTemplate.strength! > 150));
  };

  /** 数据处理 */
  const initPreviewInfo = async () => {
    try {
      const res = await fetchApi.previewPlan(previewId);
      const subject = res.subject;
      fileInfoRef.current = res.planFileModelList.reduce<CommonObj<string>>((pre, cur) => ({...pre, [cur.name]: `${downFileUrl}${cur.localMainKey}`}), {});
      formRef.current?.setFieldsValue({
        ...subject,
        dateOfBirth: subject.dateOfBirth? moment(subject.dateOfBirth): undefined,
        subjectName: `${subject.lastName}${subject.firstName}`,
        markName: (res.planInfo.markName || '').slice(0,30),
      });
      const spot_list = initSpotList(res.planInfo.treatmentTargets)
        .map((v, i) =>
          ({
            ...v,
            key: `${v.name}-${v.vertexIndex}-${i}`,
            isActive: false,
            hasError: getStrengthError(v.stimulateTemplate),
          }));
      setSpotList(spot_list);
      setPlanInfo(res);
    } catch (error) {
      //
    } finally {
      setLoadStatus(LoadingEnum.request | loadStatusRef.current);
    }
  };

  const handleSubmit = async () => {
    await ValidateExpireModal(planInfo?.expireTime, () => {
      history.push('/point');
    }, '方案已过期，无法创建治疗任务');
    await formRef.current?.validateFields();
    setLoadStatus(loadStatusRef.current & (LoadingEnum.request ^ LoadingEnum.success));
    try {
      const error_item = spotList.find(v => v.hasError);
      if(error_item) {
        handleClickSpot(error_item);
        handleScrollList(error_item);

        return;
      }
      const subject_value = formRef.current?.getFieldsValue();
      const lastName = subject_value.subjectName.slice(0, 1);
      const firstName = subject_value.subjectName.slice(1);
      const markName = subject_value.markName;
      subject_value.markName = undefined;

      const pinyinFirstName = pinyin(firstName, {
        style: pinyin.STYLE_NORMAL,
      });
      const pinyinLastName = pinyin(lastName, {
        style: pinyin.STYLE_NORMAL,
      });
      await fetchApi.submitPreviewPlan(previewId, {
        treatmentTargets: spotList.filter(v => !v.isMotion).map(v => ({
          name: v.name,
          vertexIndex: v.vertexIndex,
          strength: v.stimulateTemplate!.strength,
        })),
        subject: {
          ...subject_value,
          dateOfBirth: new Date(subject_value.dateOfBirth).getTime(),
          pinyinLastName: pinyinLastName.flat(pinyinFirstName).join(''),
          pinyinFirstName: pinyinFirstName.flat(pinyinFirstName).join(''),
          lastName,
          firstName,
        },
        markName,
      });
      leave2Home(true);
    } catch (error: any) {
      if (error?.code === 'PNT_PI10109') {
        // eslint-disable-next-line no-void
        void message.error('已存在该文件，无法再次导入');
      }
    }finally {
      setLoadStatus(loadStatusRef.current | LoadingEnum.request);
    }
  };

  const leave2Home = (isSubmit: boolean = false) => {
    history.push({
      pathname: '/point',
      state: {
        from: isSubmit? 'uploaded': '',
      }});
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    fetchApi.cancelTempPreview(previewId);
  };

  const handleClickVolume = () => {
    // do nothing, is only preview
  };

  const bindUpdateVolumeViewer = (viewer: any) => {
    volumeViewerRef.current = viewer;
  };

  const handleVolumeLoadingChange = (type: boolean) => {
    if(!type) {
      setLoadStatus(loadStatusRef.current | LoadingEnum.volume);
    }
  };

  const bindUpdateSurfaceViewer = (viewer: any) => {
    surfaceViewerRef.current = viewer;
  };

  const handleSurfaceLoadingChange = (type: boolean) => {
    if(!type) {
      setLoadStatus(loadStatusRef.current | LoadingEnum.surface);
      drawDot();
    }
  };

  const handleClickSurface = () => {
    // do nothing, is only preview
  };

  const handleClickSpot = (spot: PreviewSpotType) => {
    const next_spot = spotList.map(v => {
      if(v.key === spot.key) {
        return {...v, isActive: true};
      }

      return {...v, isActive: false};
    });
    setSpotList(next_spot);
    drawDot(next_spot);
    handleFocus(spot.surfSeed);
    updateVolumeCursorByWorld(volumeViewerRef.current, spot.volSeed);
  };

  const handleFocus = (surf_ras: Coordinate) => {
    if (surfaceViewerRef.current) {
      surfaceViewerRef.current.focusMark('scalp_mask_1', surf_ras);
      // surfaceViewerRef.current.zoom = surfaceZoom;
    }
  };

  const handleChangeStrength = (val: number, key: string) => {
    val = val || 0;
    setSpotList(spotList.map(v => {
      if(v.key === key) {
        return {...v, hasError: getStrengthError({strength: val}) ,stimulateTemplate: v.stimulateTemplate? {...v.stimulateTemplate, strength: val}: undefined};
      }

      return v;
    }));
  };

  const handleScrollList = (item: PreviewSpotType) => {
    if(!listRef.current) return;

    const treat_spot = spotList.filter(v => !v.isMotion);
    let index = treat_spot.findIndex(v => v.key === item.key);
    let top_height = 40;

    if(item.isMotion) {
      index += spotList.filter(v => !v.isMotion).length;
      top_height += 40;
    }
    const timer = setTimeout(() => {
      listRef.current!.scrollTop = index * 64 + top_height;
      clearTimeout(timer);
    }, 0);
  };

  const drawDot = (list = spotList) => {
    list.forEach(v => {
      updateSurfaceCursorByWord(surfaceViewerRef.current, v.surfSeed, parseInt(v.color!.replace('#', '0x'), 16), v.isActive? 3.5: 2, v.key);
    });
  };

  const removeDot = () => {
    spotList.forEach(v => {
      removerDot(surfaceViewerRef.current, v.key);
    });
  };

  const reloadPage = () => {
    removeDot();
    setLoadStatus(LoadingEnum.init);
    //
    // spotList.map(v =>)
  };

  const handleUploadOk = (id: string) => {
    setIsShowFileModal(false);
    setPreviewId(id);
    history.push(`/point/previewPlan/${id}`);
  };

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    initPreviewInfo();

    return () => {
      reloadPage();
    };
  }, [previewId]);

  useEffect(() => {
    loadStatusRef.current = loadStatus;
  }, [loadStatus]);

  useEffect(() => {
    if(!previousId) return;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    fetchApi.cancelTempPreview(previousId);
  }, [previousId]);

  return <Spin wrapperClassName={styles.container} spinning={loadStatus !== LoadingEnum.success}>
    <main>
      <UploadModal
        isShowFileModal={isShowFileModal}
        onCancel={() => setIsShowFileModal(false)}
        onOk={handleUploadOk}
      />
      <div className={styles.left_container}>
        <div className={styles.patient_info}>
          <PatientForm formRef={formRef} isPreview={false} />
        </div>
        {planInfo &&  <div className={styles.view_container}>
          <QuarterLayout isTwoColumn>
            <QuarterVolumes className={quarterLayoutStyle.volumes_two_columns}>
              <PointVolumeViewer
                vizArray={[ 'T1.mgz', 'scalp_mask.nii'].map(v => ({
                  descriptor: viewFileMap[v],
                  signedUrl: fileInfoRef.current[v],
                  artiDef: VolemeArtiDefMap[v],
                })) as any}
                panelWidth={300}
                isTwoColumn
                changePointer={handleClickVolume}
                updateViewerCb={bindUpdateVolumeViewer}
                setVolumeLoading={handleVolumeLoadingChange}
                t1Opacity={100}
                skullMaskUOpacity={100}
                propsClass={styles.volume_container}
              />
            </QuarterVolumes>
            <QuarterSurface className={classNames(quarterLayoutStyle.surface_two_columns, styles.quarter_surface)}>
              <PointSurfaceViewer
                t1Opacity={100}
                colorMap={Colormap.White}
                scalpMaskOpacity={40}
                surfaceWidth={662}
                viewZoom={100}
                view={'lateral'}
                autoRotate={{ X: false, Y: false, Z: false }}
                gridValue={{ X: false, Y: false, Z: false }}
                surfaceLoading={false}
                isBat={false}
                pialUrl={fileInfoRef.current['pial.gii']}
                scalpMaskUrl={fileInfoRef.current['scalp_mask.obj']}
                setSurfaceViewer={bindUpdateSurfaceViewer}
                setSurfaceLoading={handleSurfaceLoadingChange}
                surfaceClickPointerUpdateView={handleClickSurface}
              />
            </QuarterSurface>
          </QuarterLayout>
        </div>}
      </div>
      <div className={styles.right_container}>
        <div className={styles.list_content} ref={listRef}>
          <p className={styles.title}>治疗靶点</p>
          {spotList.filter(v => !v.isMotion).map(spot => {
            return <PreviewCommonSpot
              key={spot.key}
              name={spot.name}
              hemi={spot.hemi}
              parcType={spot.parcType}
              parcId={spot.parcId}
              volSeed={spot.volSeed}
              surfSeed={spot.surfSeed}
              vertexIndex={spot.vertexIndex}
              vertex={spot.vertex ? spot.vertex : ''}
              isUserAdd={spot.isUserAdd}
              isMotion={spot.isMotion}
              type={spot.type}
              isPreview
              isPreviewPlan
              stimulateTemplate={spot.stimulateTemplate}
              color={spot.color}
              selected={spot.selected}
              isActive={spot.isActive}
              isError={spot.hasError}
              onClick={() => handleClickSpot(spot)}
              onChangStrength={(val: number) => handleChangeStrength(val, spot.key)}
            />;
          })}
          {spotList.filter(v => v.isMotion).length !== 0 && <p className={styles.title}>MEP参考点</p>}
          {spotList.filter(v => v.isMotion).map(spot => {
            return <PreviewMotionSpot
              key={spot.key}
              name={spot.name}
              hemi={spot.hemi}
              parcType={spot.parcType}
              parcId={spot.parcId}
              volSeed={spot.volSeed}
              surfSeed={spot.surfSeed}
              vertexIndex={spot.vertexIndex}
              vertex={spot.vertex ? spot.vertex : ''}
              isUserAdd={spot.isUserAdd}
              isMotion={spot.isMotion}
              isPreview
              color={spot.color}
              selected={spot.selected}
              isActive={spot.isActive}
              stimulateTemplate={spot.stimulateTemplate}
              onClick={() => handleClickSpot(spot)}
              hiddenMotionButton
              onJumpToMeasureScope={() => {
                //
              }}
            />;
          })}
        </div>
        <div className={styles.footer_container}>
          <Button ghost type="primary" onClick={() => leave2Home(false)}>取消</Button>
          <div>
            <Button ghost type="primary" onClick={() => setIsShowFileModal(true)}>重选文件</Button>
            <Button type="primary" onClick={handleSubmit}>保存</Button>
          </div>
        </div>
      </div>
    </main>
  </Spin>;
};
