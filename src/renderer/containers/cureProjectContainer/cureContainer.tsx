/* eslint-disable no-warning-comments */
import * as React from 'react';
import styles from '../../../renderer/containers/cureProjectContainer/cureContainer.less';
import quarterLayoutStyle from '@/renderer/component/quarterLayout/quarterLayout.less';
import { Drawer, message, notification, Spin } from 'antd';
import { connect } from 'react-redux';
import { injectIntl } from 'react-intl';
import { Colormap } from '@device/pnt-component/packages/ui-component/visualization/artiDef';
import { IntlPropType } from '@device/pnt-component/packages/lib/propTypes';
import { auth, history } from '@/renderer/store/configureStore';
import { DraggableControl } from '@device/pnt-component/packages/ui-component/visualizers/control/draggableControl';
import {
  worldCoordsType,
} from '@device/pnt-component/packages/ui-component/visualizers/control/surface/point/point';
import {
  MatchPropType,
  PlanIdMatchPropType,
  ProjectIdMatchPropType,
  SubjectIdMatchPropType,
} from '@device/pnt-component/packages/index';
import { Coordinate, Hemi } from '@ngiq/ngiq-api-types';
import classnames from 'classnames';
import {
  getRelativePosition,
} from '@device/pnt-component/seeg/component/plan/planVolumeViewer/planVolumeViewerUtils';
import _ from 'lodash';
import { BrainBrowser } from '@device/pnt-component/packages/ui-component/brainbrowser/imports/brainbrowser';
import {
  voxelCoordsType,
} from '@device/pnt-component/packages/ui-component/visualizers/control/volume-pointer/volumePointer';
import {
  getVolumeColorAndColorMap,
  initColorMap,
  setAparcAsegSurfaceOfNativeDB,
  setAparcS2009sVolumeColorMap,
  setAparcVolumeColorMap,
  updateAparcA2009sAsegSurfaceColorMap,
  updateAparcAsegSurfaceColorMap,
  volumeXYZtoSurface,
} from './utils/interactionViewer';
import { SubjectInTransferState } from '@device/pnt-component/packages/store/subjectsInTransfer/types';
import { ControlInfoType, TransferOperationProgress, TransferOperationStatus } from '../../../common/types/messageTypes';
import { shouldUpdateWithoutMatch } from '@point/common/common';
import { DesktopApplicationState } from '../../../renderer/store';
import { checkIsCloud, PlanStatus200, statusNullFooter } from '@/renderer/constant/planStatus200';
import { Point200Api } from '@/common/api/point/point200Api';
import {
  CoordinateModel,
  CopyPlanModel,
  EnumCreatePlanLogModelEvent,
  EnumPlanModelCreateSource,
  EnumPlanModelPlanStatus,
  OutputFile,
  PlanModel,
  StimulateTemplateModel,
} from '../../../common/types/index.defs';
import { getRenderRightControl } from './utils/getRenderRightControl';
import {
  getArtidef,
  getFileListSurface,
  getFileListVolume,
  getLeftBrainDotLength,
  getTkras2ras,
  SurfaceFileType,
  viewFileMap,
  VolumeFileType,
} from './utils/fileUtils';
import { getPlanFileStatus } from '@/renderer/utils/getPlanFileStatus';
import { defaultViz } from '@/renderer/constant/constDefaultViz';
import {
  getCuringListBasePatientOfNative,
  getPatientIdOfNative,
  getPlanIdOfNative,
  getPlanListINPatientOfNative,
  removeCuringOfNative, removePlanListOfNative, removePlanOfNative,
  setFileOfNative,
  setLeftVertexNumberOfNative, setMotionSpotOfNative,
  setNeedUpdateRemainTreatmentCount,
  setPatientOfNative,
  setPlanOfNative,
  setPlanTkras2rasOfNative,
  setTreatLogs,
  updatePatientRecentAtOfNative,
} from '@/renderer/utils/offline/dbSql';
import { getPoint200Api } from '../../../common/api/point/point200ApiUtils';
import { checkOfflineLevel } from '@/renderer/utils/offline/getOfflineStatus';
import { clearCoilContainerPageFlag, pushNativeLogs } from '@/renderer/utils/offline/offlineLog';
import { getProductionLine, ISLOCATIONDEBUG } from '@point/utils/getProductionLine';
import moment from 'moment';
import { addSkipNumber, checkPointInScalp, clearSkipNumber, needSkipMessage } from './utils/checkPointInScalp';
import { CommonSpot, cureContainerType, SpotType } from '@/renderer/utils/pageType';
import { tmsSocket } from '../../component/positioningMeasurement/empTargets/tmsSocket';
import {
  initSpotList,
  updateSurfaceCursorByWord,
  updateVolumeCursorByWorld,
  readJsonFile, clearSurfaceDot, setNativeResting, sniffer, openJsonFile,
  removerDot,
} from './utils/utils';
// import { CardHeader } from './components/cardHeader';
import { filterRenderPlanList, getSafePlan } from './utils/getSafePlanOfKiosk';
import { T1Processed } from './components/T1Processed';
import { ISTESTNEXT } from '../coil/coilContainerUtils';
import { HeaderCom } from './components/headerCom';
import { PatientPage } from './components/patientPage';
import { ColorAtla } from './components/colorAtla';
import { colors } from './utils/colors';
import { EditCurePage } from './components/editCurePage';
import { PreviewCurePage } from './components/previewCurePage';
import { DiseaseFilterPage } from './components/diseaseFilterPage';
import { EditFooter } from './components/editFooter';
import { PreviewFooter } from './components/previewFooter';
import { getVertexByPosition } from './utils/surfaceUtils';
import { QuarterLayout, QuarterSurface, QuarterVolumes } from '../../component/quarterLayout/quarterLayout';
import { PointVolumeViewer } from '../../component/pointVolumeViewer/pointVolumeViewer';
import { PointSurfaceViewer } from '../../component/pointSurfaceViewer/pointSurfaceViewer';
import { VizControl } from './components/vizControl';
import { CureNum } from './components/cureNum';
import { LogModel } from './components/logModel';
import { getDeviceControlInfo, SendSetHistoryLogs } from '../../ipc/senders';
import { dataOfTreatLogPatient, DeviceType, fileRootPath, getDeviceType } from '../../utils/utils';
import { getAge } from '../../../point/utils/getAgeByBirth';
import { createLogEvent } from '@/renderer/containers/cureProjectContainer/utils/createLog';
import { getRealRemainCount, setSaveParams } from './utils/setSaveParams';
import { TerminationModal } from './components/terminationModal';
import { GoBackModal } from '@/renderer/containers/cureProjectContainer/components/goBackModal';
import { jumpToEmgTmsPage } from '@/renderer/containers/cureProjectContainer/utils/jumpToMotionScope';
import { jumpToTreatment } from '@/renderer/containers/cureProjectContainer/utils/jumpToTreatment';
import { beyondAllEntryPoint } from './utils/beyondCalMinEntryPoint';
import { PlanListModal } from './components/planList';
import { surfCoord2VolCoord } from '@/renderer/containers/cureProjectContainer/utils/surfCoord2VoCoord';
import { treatmentCounts } from '../../route/pointRouter';
import { NgSelectFileModal } from '../../component/NgSelectFileModal';
import { handleGetFolderInfo } from '../../component/NgFileList/utils';
import { DeviceListModelView } from '../planList/components/deviceLogModal';
import { v4 as uuidv4 } from 'uuid';
import { ValidateExpireModal } from '../../component/ValidateExpireModal/validateExpireModal';

export enum SyncDisplayType {
  SurfaceView = 'Surface View',
  SurfaceVolume = 'Surface+Volume View'
}

export type PatientIdMatchPropType = MatchPropType<{ patientId?: string; planId?: string }>;
type CureProjectPropsType = IntlPropType & PatientIdMatchPropType & SubjectIdMatchPropType & ProjectIdMatchPropType & PlanIdMatchPropType & {
  fileTransfer: SubjectInTransferState;
};
type CureProjectStateType = cureContainerType & { isNotComplete: boolean; fileModalVisabled: boolean; isPtcPlan: boolean; logsVisible?: boolean; isControl?: boolean; isShowEdit?: boolean; staticSpot?: SpotType; isChanged?: boolean; treatmentDaysExceeded?: boolean }; // 加一个未完成逻辑， 控制按钮点击
const {isKiosk, isPoint200, isPoint100} = getProductionLine();
let offlinePlanStatus = isPoint200 ? [PlanStatus200.Usable, PlanStatus200.Using] : [PlanStatus200.Usable, PlanStatus200.Using, PlanStatus200.NeedConfirm, PlanStatus200.ProcessFinished];
export class IntlCureContainer extends React.Component<CureProjectPropsType, CureProjectStateType> {
  private point200Api: Point200Api;
  private updateFirstDotTimer?: any;
  private rollInterval?: any;
  private readonly viewerRef: React.RefObject<HTMLDivElement>;
  private readonly gridRef: React.RefObject<HTMLDivElement>;
  private timer: number | undefined;
  private spotListWrap: React.RefObject<any>;
  private onStartThrottle: any;
  private formList: (React.RefObject<any> | undefined)[];
  private contextUrl?: string;

  public shouldComponentUpdate(prevProps: CureProjectPropsType, prevState: CureProjectStateType) {
    if(prevState.planId !== this.state.planId) return true;

    return shouldUpdateWithoutMatch(prevProps, this.props, prevState, this.state);
  }

  public async componentDidUpdate(prevProps: CureProjectPropsType, prevState: CureProjectStateType) {
    const { operation } = this.props.fileTransfer.subjectsInTransfer;
    // @ts-ignore
    const upload = operation.filter((obj: TransferOperationProgress) => obj.type === 'upload' && obj.subjectId === this.state.patientId &&
      (obj.status === TransferOperationStatus.Processing ||
        obj.status === TransferOperationStatus.ReProcessing ||
        obj.status === TransferOperationStatus.PauseByApp ||
        obj.status === TransferOperationStatus.PauseByUser));
    if (prevState.uploadCard.length !== upload.length) {
      this.setState({ uploadCard: upload });
      await  this.initPatient();
    }
  }

  constructor(props: CureProjectPropsType) {
    super(props);
    this.viewerRef = React.createRef();
    this.gridRef = React.createRef();
    this.timer = undefined;
    let initPlanId =  parseInt(this.props.match.params.planId!, 10);
    let initShowPatient = history.location.search.indexOf('showPatient=1') > 0 || !!window.localStorage.getItem('backCurePageNeedOpenDrawer');
    this.formList = [];

    this.state = {
      patientId: parseInt(this.props.match.params.patientId!, 10),
      planId: initPlanId,
      projectId: parseInt(this.props.match.params.projectId!, 10),
      patient: undefined,
      productType:200,
      showPatch: false,
      selectedColorAtlaType: 'aprac',
      loading: !initShowPatient,
      loadedTkras2ras: false,
      showControlIndex: -1,
      viewerGridHeight: 0,
      viewerGridWidth: 0,
      spotList: [],
      panelWidth: 0,
      isEdit: false,
      isModalVisible: false,
      isModalVisibleTermination: false,
      vizArray: [],
      surfaceVizList: [],
      colors: [],
      isTwoColumn: true,
      volumeOverlayIndex: 0,
      showDrawer: initPlanId === -1 || initShowPatient,
      notAllowCloseDrawer: initPlanId === -1,
      selectedColorMapPatch: -1,
      selectedCard: 'cureResult',
      plans: [],
      uploadCard: [],
      treatmentCount: 0,
      remainTreatmentCount: 0,
      isShowLog: false,
      haveBold: false,
      haveT1: false,
      restingPlanIds:[],
      clickFrom: 'spot',
      planListVisible: false,
      planList: [],
      fromPlanList: false,
      isNotComplete: false, // 初始都有数据
      isPtcPlan: false,
      fileModalVisabled: false,
      logsVisible: false,
      treatmentDaysExceeded: false, // 治疗天数是否已达上限
    };
    this.onStartThrottle = _.throttle(this.onStart, 1500, { trailing: true });
    const jwtToken = auth.jwtToken();
    this.point200Api = getPoint200Api(jwtToken!);
    this.spotListWrap = React.createRef();
  }

  public async componentDidMount() {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.getControlInfo();
    if(this.state.planId === -1){
      await this.initPatient();
      await this.loadColorMap();
      this.setState({loading: false, volumeLoading: false, surfaceLoading: false});
    }else{
      try {
        await this.initPatient();
        await Promise.all([
          this.initFetch(true),
          this.loadColorMap(),
        ]);
      }catch (e) {
        this.setState({ loading: false, volumeLoading: false, surfaceLoading: false });
      }
      // 有网时嗅探，查看是否被其他设备治疗；进入本机检查点，同步脱网数据
      if(checkOfflineLevel() === 'level3' && localStorage.getItem('network') === 'online'){
        const {projectId, planId, patientId} = this.state;
        await sniffer({projectId, patientId, planId}, this.props.intl, this.point200Api, isKiosk);
      }
    }
    this.rollInterval = setInterval(this.checkViewerGridWidth, 500);
    document.addEventListener('mousemove', this.mouseMove);
    localStorage.removeItem('backCurePageNeedOpenDrawer');
    await clearCoilContainerPageFlag();
  }

  public async componentWillUnmount() {
    if (this.rollInterval) clearInterval(this.rollInterval);
    clearSkipNumber();
    // fix Warning: Can't perform a React state update on an unmounted component
    this.setState = () => undefined;
    if (this.updateFirstDotTimer) clearTimeout(this.updateFirstDotTimer);
    removeEventListener('mousemove', this.mouseMove);
  }

  private getControlInfo = async () => {
    const info: ControlInfoType = await getDeviceControlInfo();
    this.setState({
      isControl: info.isControl && (auth.isOrgAdmin() || auth.isNGSupporter()),
      isShowEdit: info.planConfig.editPlan,
    });
  };

  private handlePlanListShow = async () => {
    this.setState({ planListVisible: true });
    // eslint-disable-next-line no-void
    await this.getAnotherReviewGroup();
  };

  public render() {
    const { patient, uploadCard, projectId, planId, plans, isModalVisibleTermination, loading, isTwoColumn, showPatch, showDrawer, patientId, volumeLoading, surfaceLoading, isShowLog, isModalVisible, planInfo, fromPlanList, fileModalVisabled, logsVisible, isEdit } = this.state;

    return <>
      <Spin spinning={loading || volumeLoading || surfaceLoading}>
        <div className={classnames(styles.project_container,'project_container')}>
          <HeaderCom
            planInfo={planInfo}
            patient={patient}
            onChangeShowPatient={this.showPatientDrawer}
            onChangeShowColorMap={this.onChangeShowPatchColorMap}
            onChangeLayout={this.handleLayout}
            goBack={this.goBack}
            showDrawer={showDrawer}
            showPatch={showPatch}
            isFourLayout={!isTwoColumn}
            anotherReviewGroupCount={this.state.anotherReviewGroupCount}
            handlePlanListShow={this.handlePlanListShow}
            fromPlanList={fromPlanList}
            gotoEmg={this.gotoEmg}
          />
          <div className={styles.content_container}>
            <div className={styles.left_container} ref={this.viewerRef}>
              <div
                ref={this.gridRef}
                className={styles.viewer_grid}
                onMouseMove={this.bindMouseMove}
                onMouseLeave={this.bindMouseLeave}
              >
                { this.renderVizControl() }
                <div className={styles.volume_wrap}>
                  {this.renderViewer()}
                </div>
              </div>
            </div>
            {showPatch && this.renderColorMap()}
            <div className={styles.right_container} style={{ width: isKiosk ? '520px' : '490px' }}>
              {this.renderControl()}
            </div>
          </div>
          <Drawer
            placement={'left'}
            width={isKiosk ? 600 : 530}
            closable={false}
            onClose={() =>this.showPatientDrawer(false)}
            visible={showDrawer}
            key={'left'}
            getContainer={false}
            className={styles.drawer}
            style={{ position: 'absolute' }}
          >
            <PatientPage
              uploadCards={uploadCard}
              patientInfo={patient}
              plans={plans}
              selectedPlanId={planId!}
              projectId={projectId!}
              patientId={patientId!}
              fetchPlans={this.fetchPlans}
              clickPlan={this.clickPlan}
              ensureFromPlanList={this.ensureFromPlanList}
            />
          </Drawer>
        </div>
      </Spin>
      <TerminationModal visible={isModalVisibleTermination} handleOk={this.terminationHandleOk} handleCancel={this.terminationHandleCancel} />
      {projectId && planId && isShowLog && <LogModel visible={isShowLog} projectId={projectId} subjectId={patientId!} planId={planId} onClose={this.onCloseLogModel}/>}
      <GoBackModal visible={isModalVisible} handleOk={this.closeGoBackModal} handleCancel={this.closeGoBackModal} />
      <PlanListModal
        visible={this.state.planListVisible}
        planList={this.state.planList}
        patientId={planId}
        clickPlan={this.clickPlan}
        ensureFromPlanList={this.ensureFromPlanList}
        handleOnCancel={() => this.setState({ planListVisible: false })}
      />
      <DeviceListModelView visible={logsVisible!} subjectId={patient?.patientCode!} planId={planId} handleClose={this.hiddenLogs} />
      <NgSelectFileModal
        filepath={fileRootPath}
        handleError={this.handleFileError}
        visible={fileModalVisabled}
        // uploadType={isZip ? UploadType.uplaod}
        onCancel={() => this.setState({
          fileModalVisabled: false,
        })}
        extName='.json'
        onOk={(files) => {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          this.onImportPlan(files[0].path);
        }}
      />
      {isEdit && <div onClick={this.handleEditMaskClick} className={styles.edit_mask} />}
    </>;
  }

  private handleEditMaskClick = () => {
    message.destroy();

    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    message.warning('请退出编辑后操作');
  };

  private gotoEmg = () => {
    if(!ISLOCATIONDEBUG){
      if (isKiosk && (!tmsSocket?.beatType || !tmsSocket.hasBat)) {
        return;
      }
    }

    const record = this.state.patient!;
    window.localStorage.setItem('backCurePageNeedOpenDrawer', '1');

    return history.push(`/point/previewCureProject/project/${record.departmentId}/patient/${record.id}`);
  };

  private hiddenLogs = () => {
    this.setState({
      logsVisible: false,
    });
  };

  private getFilePath = async () => {
    if (getDeviceType() === DeviceType.linux) {
      try {
        const list = await handleGetFolderInfo(fileRootPath);
        if (list.length === 0) throw Error('未检测到移动设备');
        this.setState({
          fileModalVisabled: true,
        });
      } catch (error) {
        this.handleFileError(error as string);
      }
    } else {
      const filePath = await openJsonFile();
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.onImportPlan(filePath);
    }
  };

  private handleFileError = (error?: string) => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    message.error('未检测到移动设备');
  };

  private getPlanCallback = async () => {
    // eslint-disable-next-line no-console
    console.log('getPlanSuccess---run');
    await this.initPatient();
    await this.initFetch();
    await this.getAnotherReviewGroup();
  };

  private getAnotherReviewGroup = async () => {
    if (isKiosk) return;
    const { projectId, planId, patientId } = this.state;
    if(!projectId || !planId || !patientId) return;
    const { records } = await this.point200Api.getAnotherReviewGroup(projectId, patientId, planId) || { records: [] };
    this.setState({ planList: records });
  };

  private closeGoBackModal = () => {
    this.setState({ isModalVisible: false });
  };
  private terminationHandleOk = async () => {
    const { planId, projectId, patientId } = this.state;
    if(!projectId || !patientId || !planId) return;
    await this.createLog(EnumCreatePlanLogModelEvent.TerminateTreatment, planId);
    // 终止治疗，检查如果休息中移出，检查如果需要执行 治疗次数+1，移出
    await setNeedUpdateRemainTreatmentCount(planId, false);
    await removeCuringOfNative(planId);
    if(isPoint100){
      // 移出本机数据
      await removePlanOfNative(projectId, patientId, planId);
    }
    await this.point200Api.stopPlanById(projectId, patientId, planId);
    SendSetHistoryLogs('终止治疗，退回首页');
    history.push('/point');
  };

  private mouseMove = (e: any) => {
    if (e.pageX < 23 && !this.state.showDrawer) {
      if (!this.timer) {
        this.timer = new Date().getTime();
      } else {
        let newTime = new Date().getTime();
        if (newTime - this.timer > 800) {
          this.setState({ showDrawer: true });
        }
      }
    } else {
      this.timer = undefined;
    }
  };

  private terminationHandleCancel = () => {
    this.setState({
      isModalVisibleTermination: false,
    });
  };

  private goBack = () => {
    const { planInfo, isEdit } = this.state;
    if (!planInfo) {
      SendSetHistoryLogs('方案详情页面，点击返回');
      history.push('/point');

      return;
    }

    if (!isEdit) {
      SendSetHistoryLogs('方案详情页面，点击返回');
      history.push('/point');

      return;
    }
  };

  private initPatient = async () => {
    // eslint-disable-next-line no-console
    console.log('initPatient---run');
    const { patientId, projectId, planId } = this.state;
    try{
      if(checkOfflineLevel() === 'level2'){
        throw new Error('offline');
      }
      const [ patientInfo, plans ] = await Promise.all([
        this.point200Api.getPatient(patientId!),
        this.point200Api.getPlanListOfPatient(projectId!, patientId!),
      ]);
      let restingPlanIds = await getCuringListBasePatientOfNative(patientId!);
      let newPlans = plans.records;
      // 本地有网，写入数据
      if (checkOfflineLevel() === 'level3' || ISTESTNEXT) {
        await setPatientOfNative(patientInfo.id, patientInfo);
      }
      let firstSafePlan = getSafePlan(newPlans);
      // 有网和无网，关闭弹窗的逻辑不同
      this.setState({
        patient: patientInfo,
        projectId: patientInfo.departmentId,
        patientId: patientInfo.id,
        plans: filterRenderPlanList(newPlans),
        planId: (planId && planId > 0) ? planId : firstSafePlan ? firstSafePlan.id : -1,
        notAllowCloseDrawer: !firstSafePlan,  // 检查方案都不属于可渲染脑图的状态，不可以关闭抽屉
        restingPlanIds,
      }, async() =>{
        if(parseInt(this.props.match.params.planId!, 10) === -1 && firstSafePlan?.id){
          await this.initFetch();
        }
      });
    }catch (e) {
      if(!isKiosk) return;
      let plans = await getPlanListINPatientOfNative(projectId!, patientId!);
      let newPlans = plans.filter((item: PlanModel) => [PlanStatus200.Usable, PlanStatus200.Using, PlanStatus200.NeedConfirm].includes(item.planStatus)).map((item: PlanModel) => {
        return item;
      });
      let offlinePatient = await getPatientIdOfNative(patientId!);
      if (!offlinePatient.id) {
        // eslint-disable-next-line no-void
        void message.warning('服务异常，请重启或联系技术支持：503');

        return;
      }
      let length = newPlans.filter((subItem: PlanModel) => subItem.subjectId === offlinePatient.id).length;
      let firstSafePlan = getSafePlan(newPlans);
      this.setState({
        patient: Object.assign({}, offlinePatient, {offlinePlanNum: length}),
        plans: filterRenderPlanList(newPlans),
        planId: (planId && planId > 0) ? planId : firstSafePlan ? firstSafePlan.id : -1,
        notAllowCloseDrawer: length < 1,  // 脱网状态下，本机有plan可关闭
      },async () => {
        this.forceUpdate();
        if(parseInt(this.props.match.params.planId!, 10) === -1 && length){
          await this.initFetch();
        }
      });
    }finally {
      await updatePatientRecentAtOfNative(patientId!);
    }
  };

  private jumptoHome = () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    message.error('方案已更新，请使用新方案进行治疗！');
    history.push('/');
  };

  private initFetch = async (isFirstRender = false, showConfirm?: boolean) => {
    // eslint-disable-next-line no-console
    console.log('initFetch---run');
    const { planId, projectId, patientId } = this.state;
    const {intl} = this.props;
    if (planId === -1) return;
    let res: (PlanModel & { departmentName?: string }) | undefined;
    res = await getPlanIdOfNative(projectId!, patientId!, planId!);
    if (checkOfflineLevel() === 'level2') {
      if (!res) {
        // eslint-disable-next-line no-void
        void message.warning(this.props.intl.formatMessage({id:'point200.offline.noPlanInNative'}));

        return;
      }
    } else {
      try{
        const resOnline = await this.point200Api.getPlanById(projectId!, patientId!, planId!);
        if (resOnline.hasDeprecated) {
          this.jumptoHome();

          return;
        }
        if (!res && checkOfflineLevel() === 'level3' && isFirstRender) {
          // 初次加载，需要时间长时的提示
          // eslint-disable-next-line no-void
          void message.warning(this.props.intl.formatMessage({id:'point200.offline.firstLoading'}));
        }
        res = resOnline;

        if(showConfirm && res && res.confirmedByUser){
          let confirmedAt = moment(res.statusChangedAt).format('YYYY-MM-DD');
          let name = res.confirmedByUser.username ? res.confirmedByUser.username : res.confirmedByUser.firstName;
          const label1 = intl.formatMessage({id:'point.cureProject.confirmByUser'}, {value: name});
          const label2 = intl.formatMessage({id:'point.cureProject.confirmByDevice'}, {value: confirmedAt});
          // eslint-disable-next-line no-void
          void message.success(`${label1}${label2}`);
        }
      } catch (e) {
        res = await getPlanIdOfNative(projectId!, patientId!, planId!);
      }
    }
    // 治疗端有网，备份一份数据, 只备份，可用于治疗的方案
    const tempRes = res as PlanModel;
    tempRes.planInfo.expireTime = res?.expireTime;
    if ((checkOfflineLevel() === 'level3' || ISTESTNEXT) && offlinePlanStatus.includes(tempRes.planStatus)) {
      await setPlanOfNative(tempRes.id, tempRes);
    }
    await this.processData(tempRes);
    // if (!tempRes.dataProcessings) return;

    let { T1 } = getPlanFileStatus(tempRes.dataProcessings || []);

    if (tempRes.planFileModelList) {
      T1 = true;
    }
    this.setState({
      haveT1: T1,
      haveBold: false,
      canRequestAnotherReview: res?.canRequestAnotherReview,
      anotherReviewGroupCount: res?.anotherReviewGroupCount,
    });

    if (!T1) {
      // eslint-disable-next-line no-void
      void message.error(this.props.intl.formatMessage({id:'point200.offline.noT1'}));

      this.setState({
        loading: false,
        surfaceLoading: false,
        volumeLoading: false,
      });

      return;
    }

    const ptcFileList = (tempRes.planFileModelList || []).map(v => ({
      descriptor: viewFileMap[v.name],
      fileId: v.id,
      logicalName: v.name,
      s3MainKey: v.s3mainKey,
    }));

    const fileList = tempRes.outputFiles?.length ? tempRes.outputFiles : ptcFileList;

    await this.downloadFiles(fileList, false);

    // 检查治疗天数限制
    await this.checkTreatmentDaysLimit();
  };

  private processData = async (res: PlanModel) => {
    const restingPlanIds = await getCuringListBasePatientOfNative(this.state.patientId!);
    if (res.planInfo) {
      let planInfo = res.planInfo;
      let treatmentCount = planInfo.treatmentCount ? planInfo.treatmentCount : treatmentCounts;
      this.setState({
        createdByUserId: res.createdByUserId,
        planInfo: planInfo ,
        planStatus: restingPlanIds && restingPlanIds.includes(res.id) ? 60 : res.planStatus,
        productType: res.productType,
        leftVertexNumber18: 147477,
        spotList: initSpotList(planInfo.treatmentTargets, planInfo.snapshotConfirmedTreatmentTargets),
        treatmentCount,
        remainTreatmentCount: planInfo.remainTreatmentCount !== undefined ? planInfo.remainTreatmentCount : treatmentCount,
        isEdit: false,
        isPtcPlan: true,
      });
    }
  };

  private renderViewer = () => {
    const {
      panelWidth,
      viewerGridHeight,
      vizArray,
      pialUrl,
      scalpMaskUrl,
      isTwoColumn,
      selectedColorAtlaType,
    } = this.state;
    const quarterVolumesClass = isTwoColumn ? quarterLayoutStyle.volumes_two_columns : quarterLayoutStyle.volumes;
    const quarterSurfaceClass = isTwoColumn ? quarterLayoutStyle.surface_two_columns : quarterLayoutStyle.surface;
    const volumeSizeMultiple = isTwoColumn ? 3 : 2;
    const volumePanelWidth = panelWidth > (viewerGridHeight / volumeSizeMultiple) ? (viewerGridHeight / volumeSizeMultiple) : panelWidth;
    const surfPanelWidth = isTwoColumn ? panelWidth * 2 : panelWidth;
    const vizOverlayAtri = this.getVolumeOverlayArtiForRender(selectedColorAtlaType);

    return <QuarterLayout onChangePanelWidth={this.bindChangePanelWidth} isTwoColumn={isTwoColumn}>
      <QuarterVolumes className={classnames(quarterVolumesClass)}>
        <PointVolumeViewer
          isTwoColumn={isTwoColumn}
          skullMaskUOpacity={100}
          t1Opacity={100}
          isShowColor={false}
          vizArray={vizArray}
          showScalp
          panelWidth={volumePanelWidth}
          vizOverlayAtri={vizOverlayAtri}
          setVolumeLoading={this.bindSetVolumeLoading}
          changePointer={this.bindChangePointer}
          updateViewerCb={this.bindUpdateVolumeViewerCb}
          updateOverlayIndex={this.bindVolOverlayIndex}
          updateScalpMaskIndex={this.bindScalpMaskIndex}
        />
      </QuarterVolumes>
      <QuarterSurface className={classnames(quarterSurfaceClass)}>
        {(pialUrl && scalpMaskUrl) && <PointSurfaceViewer
          intl={this.props.intl}
          t1Opacity={100}
          colorMap={Colormap.White}
          scalpMaskOpacity={40}
          surfaceWidth={surfPanelWidth}
          viewZoom={80}
          view={'lateral'}
          autoRotate={{ X: false, Y: false, Z: false }}
          gridValue={{ X: false, Y: false, Z: false }}
          surfaceLoading={false}
          isBat={false}
          pialUrl={pialUrl}
          scalpMaskUrl={scalpMaskUrl}
          setSurfaceViewer={this.setSurfaceViewer}
          setSurfaceLoading={this.setSurfaceLoading}
          surfaceClickPointerUpdateView={this.bindSurfaceClick}/>
        }
      </QuarterSurface>
    </QuarterLayout>;
  };

  // private renderCardHeader = () => {
  //   const {intl} = this.props;
  //   let options = [
  //     { value: 'cureResult', text: intl.formatMessage({ id: 'point.audit.card.cureResult' }) },
  //   ];
  //   const { selectedCard } = this.state;

  //   return <CardHeader options={options} selected={selectedCard} onChangeCard={this.onChangeCard}/>;
  // };
  private renderControl = () => {
    const {selectedCard, spotList, planInfo, createdByUserId, isEdit, planId, planStatus, patient, productType} = this.state;
    let { renderEmpty, renderT1Success, renderPreview, renderEdit, renderFilterDisease } = getRenderRightControl(planStatus!, selectedCard, isEdit);
    if(planId === -1){
      return <></>;
    }
    let isCureCard = selectedCard === 'cureResult';

    return <DraggableControl
      title="View Control"
      isNarrow
      style={{ width: isKiosk ? '520px' : '490px', paddingBottom: '6px' }}
      isShowTitle={false}
      footer={this.renderControlFooter}
    >
      <div className={styles.content1_container}>
        {/* <div className={styles.right_header_container}>
          {this.renderCardHeader()}
        </div> */}
        <div className={styles.spot_list_area} ref={this.spotListWrap}>
          { renderEmpty && this.renderEmpty()}
          { renderT1Success && isCureCard && <T1Processed/>}
          { (renderPreview || renderEdit) && isCureCard && this.renderCureNum()}
          {renderEdit && isCureCard && <EditCurePage onReset={this.onResetCard} onSave={this.onSaveCard} formList={this.formList} patient={patient} planStatus={planStatus!} spotList={spotList} onClickSpot={this.onClickEditSpot} onUpdateBaseField={this.onUpdateBaseField} onUpdateTemplate={this.onUpdateTemplate} createdByUserId={createdByUserId} onChangeCoord={this.onChangeCoord} onDeleteSpot={this.onDeleteSpot} isOnlyEditTemplateParams={false}/>}
          {renderPreview && isCureCard && <PreviewCurePage spotList={spotList} onClickSpot={this.onClickPreviewSpot} createdByUserId={createdByUserId!} onJumpToMeasureScope={this.onJumpToMeasureScope} />}
          { renderFilterDisease && !isCureCard && planInfo && <DiseaseFilterPage planInfo={planInfo} productType={productType}/>}
        </div>
      </div>
    </DraggableControl>;
  };

  private showLogs = () => {
    this.setState({
      logsVisible: true,
    });
  };

  private renderEmpty = () => {
    return <div className={styles.empty_container}>{this.props.intl.formatMessage({ id: 'point.cureProject.invalidProject' })}</div>;
  };
  private renderCureNum = () => {
    const { isEdit, treatmentCount, remainTreatmentCount } = this.state;

    return <CureNum isEdit={isEdit} treatmentCount={treatmentCount} remainTreatmentCount={remainTreatmentCount}
      onRef={(ref: any) => {
        //
      }}/>;
  };

  private getNewPlanRequest = async (formParam: { markName: string; requestAnotherReviewComment: string }): Promise<PlanModel | void> => {
    const { planId, projectId, patientId } = this.state;
    if (!projectId || !patientId || !planId) return Promise.resolve();

    return this.point200Api.getNewPlan(projectId, patientId, planId, formParam);
  };

  private renderControlFooter = () => {
    const {
      selectedCard, isNotComplete, planStatus,
      isEdit, spotList, patientId, planId, planInfo,
      productType, haveBold, treatmentCount,
      remainTreatmentCount, startLoading,
      isPtcPlan,
      isControl,
      isShowEdit,
    } = this.state;
    if (selectedCard === 'diseaseFilter' || (planId === -1) || !planStatus) {
      return <></>;
    }
    if(statusNullFooter.includes(planStatus)){
      return <></>;
    }

    let disabledStartButton = spotList.every((item: SpotType)=> item.isMotion);
    if (!patientId || !planId) {
      disabledStartButton = true;
    }
    // 如果治疗天数已达上限，禁用开始治疗按钮
    if (this.state.treatmentDaysExceeded) {
      disabledStartButton = true;
    }
    const isCloud = isPoint200 ? checkIsCloud(planStatus, planInfo!) : false;

    if(isEdit){
      const isSaveDisabled = !spotList || spotList.length ===0;

      return <EditFooter isNotComplete={isNotComplete} isCloud={isCloud} planStatus={planStatus} productType={productType} saveDisabled={isSaveDisabled} onAdd={this.onAddSpot} onCancel={this.onCancelEdit} onSave={async (isSaveAs: boolean) => this.onSave(isSaveAs)} onImport={this.getFilePath}/>;
    }else{
      return <PreviewFooter
        treatmentIndex={treatmentCount - remainTreatmentCount}
        planStatus={planStatus}
        productType={productType}
        disabledStartButton={disabledStartButton}
        isCloud={isCloud}
        haveBold={haveBold}
        startLoading={!!startLoading}
        onTermination={this.onTermination}
        onEdit={this.onEdit}
        onStart={this.onStartThrottle}
        onLog={this.onLog}
        onEnsure={this.onEnsure}
        onRefuse={this.onRefuse}
        onUploadBold={this.onAddUploadBold}
        haveSpot={!!spotList.length}
        onReport={this.onReport}
        canRequestAnotherReview={this.state.canRequestAnotherReview}
        getNewPlanRequest={this.getNewPlanRequest}
        planInfo={this.state.planInfo}
        planId={planId}
        showDrawer={this.state.showDrawer}
        getPlanCallback={this.getPlanCallback}
        isPtcPlan={isPtcPlan}
        showLogs={this.showLogs}
        isControl={isControl}
        isShowEdit={isShowEdit}
      />;
    }
  };

  private fetchPlans = async () => {
    const { projectId, patientId } = this.state;
    if(!projectId || !patientId) return;
    let plans = [];
    if(localStorage.getItem('network') === 'online'){
      let res = await this.point200Api.getPlanListOfPatient(projectId, patientId);
      plans = res.records;
    }else{
      plans = await getPlanListINPatientOfNative(projectId, patientId);
      plans = plans.filter((item: PlanModel) => [PlanStatus200.Usable, PlanStatus200.Using, PlanStatus200.NeedConfirm].includes(item.planStatus));
    }
    let newPlans = plans;
    this.setState({ plans: filterRenderPlanList(newPlans)});
    // 检查导致治疗完成，移出本机缓存的数据
    if(plans.some((item: PlanModel) => [PlanStatus200.Finished, PlanStatus200.Terminated].includes(item.planStatus)) && isKiosk){
      await removePlanListOfNative(plans.filter((item: PlanModel) => [PlanStatus200.Finished, PlanStatus200.Terminated].includes(item.planStatus)).map((item: PlanModel) => item.id));
    }
  };

  private ensureFromPlanList = (fromPlanList: boolean) => {
    this.setState({ fromPlanList });
  };

  // 考虑采用一次性获取plans，然后这里不从后端请求了，直接使用缓存中的数据。
  // 但是surgInputs 这个属性的缓存过大，要求plans数据的长度不大于10个才可以。
  private clickPlan = async (planId: number, planStatus: EnumPlanModelPlanStatus) => {
    this.setState({ planId, showDrawer: false, loading: true, startLoading: false }, async() =>{
      await this.initFetch(false, false);
      await this.fetchPlans();
      this.onChangeShowPatchColorMap(false);
    });
    if(isKiosk && checkOfflineLevel() !== 'level2'){
      const {projectId, patientId} = this.state;
      await sniffer({projectId, patientId, planId}, this.props.intl, this.point200Api, isKiosk);
    }
  };

  private onResetCard = () => {
    const { spotList, staticSpot } = this.state;
    const isNew = spotList.some((item: SpotType) => item.isNew);
    this.setState({
      spotList: spotList.map((item: SpotType) => {
        return item.isActive ? {... isNew? item:staticSpot!, isActive: false} : {
          ...item,
        };
      }).filter((v) => !v.isNew),
      isChanged: false,
    }, () => {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.onClickEditSpot();
    });
  };

  private onSaveCard = async (uniqueId?: string, requestSpot?: SpotType[]) => {
    if(uniqueId){
      const currentSpot = this.state.spotList.find((item: SpotType)=> item.uniqueId === uniqueId);
      const index = this.state.spotList.findIndex((item: SpotType)=> item.uniqueId === uniqueId);
      if (!currentSpot) return;
      await this.formList[index]?.current?.validateFields(); // 只校验自己,别的不管
      if (!currentSpot.volSeed.x || !currentSpot.volSeed.y || !currentSpot.volSeed.z) throw new Error('信息不全');
      if (!currentSpot.isMotion && !currentSpot.stimulateTemplate?.stimulateType) {
        throw new Error('请选择刺激类型');
      }
    }
    const { projectId, patientId, planId, spotList, planInfo, surfaceViewer, leftVertexNumber18, tkras2ras } = this.state;
    if (!patientId || !projectId || !planId || !leftVertexNumber18 || !surfaceViewer || !tkras2ras || !planInfo) return;
    if(spotList.every((item: SpotType)=> item.isMotion)){
      // eslint-disable-next-line no-void
      void message.error(this.props.intl.formatMessage({id:'point.cureProject.saveErrorNoSpot'}));

      return;
    }
    let newTreatmentCount = this.state.treatmentCount;
    const spots = requestSpot || spotList;
    const remainTreatmentCount = getRealRemainCount(planInfo.treatmentCount!, newTreatmentCount, this.state.remainTreatmentCount);
    const params = await setSaveParams({ planInfo, spotList: spots, tkras2ras, leftVertexNumber18, surfaceViewer, treatmentCount: newTreatmentCount, remainTreatmentCount }, false);
    await this.point200Api.updatePlanInfo(projectId, patientId, planId, params);
    removerDot(this.state.surfaceViewer);
    message.destroy();
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    message.success('操作成功');
    this.setState({
      isChanged: false,
      spotList: spots.map(v => ({ ...v, isActive: false, isNew: false })),
    });
    // await this.initPatient();
    // await this.initFetch();
    // await this.fetchPlans();
  };

  private onReport = async () => {
    const { planId, patientId } = this.state;
    history.push(`/point/patientReport/plan/${planId}/patient/${patientId}`);
  };

  private onTermination = async () => {
    const {patientId, planId, projectId} = this.state;
    if(!patientId || !planId || !projectId) return;
    // 不需要检查休息中，台车没有终止治疗，客户端没有休息中
    let isTreatmentOther = await sniffer({projectId, patientId, planId}, this.props.intl, this.point200Api, isKiosk);
    if(!isTreatmentOther) return;
    this.setState({
      isModalVisibleTermination: true,
    });
  };

  private bindChangePanelWidth = (panelWidth: number) => {
    if (this.state.panelWidth === panelWidth) return;
    this.setState({
      panelWidth,
    });
  };

  private validateData = () => {
    if (this.state.spotList.some((item: SpotType) => item.isActive)) {
      message.destroy();
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      message.warning('请完成对当前靶点的操作');
      throw new Error('当前靶点存在编辑');
    }
  };

  private onClickEditSpot = (indexParam?: number) => {
    let { spotList } = this.state;
    if(!spotList || !spotList.length) return;
    let newSpotList = spotList.map((item: SpotType, index: number) => {
      return {
        ...item,
        isActive: index === indexParam,
      };
    });
    this.validateData();
    this.setState({
      spotList: newSpotList,
      isChanged: false,
      staticSpot: spotList.find((v, i) => i === indexParam),
    }, () => {
      this.forceUpdate();
    });
    if (typeof indexParam !== 'number') {
      removerDot(this.state.surfaceViewer);

      return;
    }
    let currentSpot = spotList[indexParam];
    if (currentSpot.volSeed) {
      let surfRas = volumeXYZtoSurface(currentSpot.volSeed, this.state.tkras2ras);
      currentSpot.surfSeed = surfRas as Coordinate;
      this.onFocusMarkInViewer(currentSpot);
    }
  };
  private onClickPreviewSpot = (spot: SpotType) => {
    let { spotList } = this.state;
    if(this.updateFirstDotTimer) return;
    let targetIndex = _.findIndex(spotList, (item: SpotType) => item.name === spot.name && item.color === spot.color);
    spotList.forEach((item: SpotType, index: number) => {
      item.isActive = index === targetIndex;
    });
    this.forceUpdate();
    this.setState({
      spotList: spotList,
    });
    let currentSpot = spotList[targetIndex];
    this.onFocusMarkInViewer(currentSpot);
  };

  private onFocusMarkInViewer = (currentSpot: SpotType) => {
    const { volumeViewer, surfaceViewer } = this.state;
    if (currentSpot && currentSpot.surfSeed && surfaceViewer && surfaceViewer.model && surfaceViewer.model.children.length) {
      this.updateMarkSurface(currentSpot);
      this.rotateSurfaceToMark(currentSpot.surfSeed);
    }
    if (currentSpot && currentSpot.volSeed && volumeViewer && volumeViewer.volumes.length >1) {
      updateVolumeCursorByWorld(volumeViewer, currentSpot.volSeed);
    }
    surfaceViewer.zoom = 0.8;
  };

  private updateMarkSurface = (currentSpot: SpotType) => {
    const { surfaceViewer } = this.state;
    let color = currentSpot.color ? parseInt(currentSpot.color.replace('#', '0x'), 16) : 0x0074D9;
    updateSurfaceCursorByWord(surfaceViewer, currentSpot.surfSeed, color);
  };

  private setSurfaceViewer = (viewer: any) => {
    this.setState({ surfaceViewer: viewer });
  };

  private setSurfaceLoading = (surfaceLoading: boolean) => {
    this.setState({ surfaceLoading }, async () => {
      this.initViewerDrawSpot();
      try{
        if(checkOfflineLevel() === 'level3'){
          setAparcAsegSurfaceOfNativeDB(this.state);
        }
      }catch (e) {
        //
      }
    });
  };

  private updateSpotList = (coord: Coordinate, surfRas: Coordinate) => {
    const { spotList, surfaceViewer, leftVertexNumber18, isEdit} = this.state;

    if(!isEdit) return spotList;
    let vertexIndex = getVertexByPosition(surfaceViewer, surfRas);
    const hemi = leftVertexNumber18 && vertexIndex >= (leftVertexNumber18 - 1) ? 'rh' : 'lh' as Hemi;
    vertexIndex = leftVertexNumber18 && vertexIndex >= (leftVertexNumber18 - 1) ? vertexIndex - (leftVertexNumber18 - 1) : vertexIndex;
    const vertex = `${hemi}${vertexIndex}`;
    this.setState({isChanged: true});

    return spotList.map((item: SpotType) => {
      if (item.isActive) {
        item.volSeed = coord;
        item.surfSeed = surfRas;
        item.worldCoords = coord;
        item.hemi = hemi;
        item.vertexIndex = vertexIndex;
        item.vertex = vertex;
        item.name = ((item.name?.startsWith('lh') || item.name?.startsWith('rh') || item.name?.startsWith('靶点')) || !item.name) ? `${vertex}` : item.name;
      }

      return item;
    });
  };

  // 在下一个循环中更新spotList
  // ps: 靶点名称修改时是失焦出发
  // 如果改成onchange,他会立刻失焦,不知道触发逻辑是什么(猜测是forceUpdate,但是太多了,而且页面靠这个驱动,动不了),完全没办法
  private updateSpotListInNextLoop = async (coord: Coordinate, surfRas: Coordinate): Promise<SpotType[]> => {
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement) {
      activeElement.blur();
    }

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(this.updateSpotList(coord, surfRas));
      }, 0);
    });
  };

  private onChangeCoord = async (coord: CoordinateModel, color: number) => {
    const {clickFrom, tkras2ras, isEdit} = this.state;
    if (clickFrom === 'mouseSurface' || clickFrom === 'mouseVolume') {
      return;
    }
    let surfRas = volumeXYZtoSurface(coord, tkras2ras);

    let spotList = (surfRas && isEdit) ? this.updateSpotList(coord, surfRas) : this.state.spotList;
    this.setState({
      spotList,
      isChanged: true,
    });
    const { volumeViewer, surfaceViewer } = this.state;
    if (surfRas) {
      updateVolumeCursorByWorld(volumeViewer, coord);
      const isEffectiveSurfaceViewer = surfaceViewer && surfaceViewer.model && surfaceViewer.model.children.length;
      if (isEffectiveSurfaceViewer) {
        updateSurfaceCursorByWord(surfaceViewer, surfRas, color);
        this.rotateSurfaceToMark(surfRas);
      }
    }
  };

  private bindChangePointer = async (patchId?: number, voxelCoords?: voxelCoordsType, worldCoords?: worldCoordsType) => {
    const { clickFrom, tkras2ras, isEdit, spotList, volumeViewer, selectedColorAtlaType } = this.state;
    if (!isEdit) return;

    if (voxelCoords && !needSkipMessage()) {
      checkPointInScalp(voxelCoords, this.props.intl, selectedColorAtlaType, volumeViewer);
    }
    if (voxelCoords && worldCoords && clickFrom === 'mouseVolume') {
      let surfSeed = volumeXYZtoSurface(worldCoords, tkras2ras);
      this.setState({
        spotList: isEdit? await this.updateSpotListInNextLoop(worldCoords, surfSeed!) : spotList,
      }, async () => {
        this.updateMark(worldCoords, surfSeed, 'mouseVolume');
      });
    }
  };
  private updateMark = (coord: Coordinate, surfRas: Coordinate | undefined, from: string, color?: number) => {
    const { spotList, isEdit } = this.state;
    if (coord && surfRas) {
      let index = spotList.length;
      let targetSpot = spotList.find((item: SpotType) => item.isActive);
      let renderColor = color ? color : parseInt(colors[index].replace('#', '0x'), 16);
      if (targetSpot && targetSpot.color && isEdit) {
        renderColor = parseInt(targetSpot.color.replace('#', '0x'), 16);
      }

      this.onlyUpdateViewMarker(coord, surfRas, renderColor, from);
    }
  };
  private onlyUpdateViewMarker = (coolSeed: Coordinate, surfSeed: Coordinate, color: number, from: string) => {
    const { volumeViewer, surfaceViewer } = this.state;
    if (volumeViewer && surfaceViewer && coolSeed && surfSeed) {
      if(from === 'mouseVolume'){
        updateSurfaceCursorByWord(surfaceViewer, surfSeed, color);
        this.rotateSurfaceToMark(surfSeed);
      }else{
        updateVolumeCursorByWorld(volumeViewer, coolSeed);
        updateSurfaceCursorByWord(surfaceViewer, surfSeed, color);
        this.rotateSurfaceToMark(surfSeed);
      }
    }
  };
  private bindSurfaceClick = async (patchId: number | undefined, worldCoords: worldCoordsType, vertex: number) => {
    const { isEdit, spotList, clickFrom } = this.state;
    if(!isEdit || clickFrom !== 'mouseSurface') return;
    let volRas = surfCoord2VolCoord(worldCoords, this.state.tkras2ras);
    if (!volRas) return;
    this.setState({
      spotList: isEdit ? await this.updateSpotListInNextLoop(volRas, worldCoords) : spotList,
    }, async () => {
      this.updateMark(volRas as Coordinate, worldCoords, 'mouseSurface');
    });
  };
  private bindSetVolumeLoading = (volumeLoading: boolean) => {
    this.setState({ volumeLoading }, () =>{
      this.initViewerDrawSpot();
    });
  };

  private bindUpdateVolumeViewerCb = (volumeViewer: any) => {
    volumeViewer.showMark = true;

    this.setState({
      volumeViewer,
    });
  };

  // start 视图顶部控制区域
  private renderVizControl = () => {
    const { showControlIndex, surfaceViewer, volumeViewer, isTwoColumn } = this.state;

    return <>{surfaceViewer && volumeViewer && <VizControl showControlIndex={showControlIndex} viz={defaultViz} surfaceViewer={surfaceViewer} isTwoColumn={isTwoColumn}
      volumeViewer={volumeViewer}/>}</>;

    return <></>;
  };

  private checkViewerGridWidth = () => {
    const { viewerGridWidth, viewerGridHeight } = this.state;
    const viewerPadding = 6;
    const width = this.gridRef.current ?
      this.gridRef.current.clientWidth - viewerPadding * 2 :
      viewerGridWidth;
    const height = this.gridRef.current ?
      this.gridRef.current.clientHeight - viewerPadding * 2 :
      viewerGridHeight;
    if ((width && width !== viewerGridWidth) ||
      (height && height !== viewerGridHeight)) {
      this.setState({
        viewerGridWidth: width,
        viewerGridHeight: height,
      });
    }
  };

  // 检查鼠标位置，判断需要渲染的 脑图控制器
  private bindMouseMove = (e: any) => {
    const { x, y } = getRelativePosition(e, this.gridRef.current);
    const { viewerGridWidth, viewerGridHeight, showControlIndex, syncDisplayType, vizArray, isTwoColumn } = this.state;
    let viewIndex = -1;
    if (isTwoColumn) {
      const width = viewerGridWidth / 3;
      const height = Math.floor(viewerGridHeight / 3);
      if (x < width && y < height) {
        viewIndex = 2;
      } else if (x >= width && x <= width * 3 && y < height * 3) {
        viewIndex = 1;
      } else if (x < width && y >= height && y <= height * 2) {
        viewIndex = 3;
      } else if (x < width && y >= height * 2 && y <= height * 3) {
        viewIndex = 4;
      }
      if (syncDisplayType === SyncDisplayType.SurfaceView) {
        viewIndex = 1;
      }
      if (viewIndex !== showControlIndex && vizArray) {
        this.setState({
          showControlIndex: viewIndex,
          clickFrom: viewIndex === 1 ? 'mouseSurface' : 'mouseVolume',
        });
      }
    } else {
      const width = viewerGridWidth / 2;
      const height = viewerGridHeight / 2;
      if (x < width && y < height) {
        viewIndex = 1;
      } else if (x >= width && x <= width * 2 && y < height) {
        viewIndex = 2;
      } else if (x < width && y >= height && y <= height * 2) {
        viewIndex = 3;
      } else if (x >= width && x <= width * 2 && y >= height && y <= height * 2) {
        viewIndex = 4;
      }
      if (syncDisplayType === SyncDisplayType.SurfaceView) {
        viewIndex = 1;
      }
      if (viewIndex !== showControlIndex && vizArray) {
        this.setState({
          showControlIndex: viewIndex,
          clickFrom: viewIndex === 1 ? 'mouseSurface' : 'mouseVolume',
        });
      }
    }
  };

  private bindMouseLeave = () => {
    this.setState({
      showControlIndex: -1,
      clickFrom: undefined,
    });
  };

  private rotateSurfaceToMark = (surRas: Coordinate) => {
    const { surfaceViewer, clickFrom, showControlIndex } = this.state;
    if ((clickFrom === 'mouseSurface' || clickFrom === 'mouseVolume') && showControlIndex === 1) return;
    if (!surfaceViewer || !surRas) return;
    surfaceViewer.focusMark('pial_gii_1', surRas);
  };

  private handleLayout = (isShow?: boolean) => {
    const { isTwoColumn } = this.state;
    this.setState({
      isTwoColumn: isShow !== undefined? isShow:!isTwoColumn,
    }, () => {
      this.forceUpdate();
    });
  };
  private onChangeShowPatchColorMap = (val: boolean) => {
    const { aparcAsegSurfaceIntensityDataUrl } = this.state;
    if (!aparcAsegSurfaceIntensityDataUrl) return;
    this.setState({
      showPatch: val,
    }, async () => {
      await this.onChangeShowPatch(val);
    });
  };
  private showPatientDrawer = (val: boolean) => {
    // 关闭时，如果不允许关闭，直接退出
    if(!val && this.state.notAllowCloseDrawer){
      return;
    }
    this.setState({
      showDrawer: val,
    });
  };
  // private onChangeCard = (val: string) => {
  //   this.setState({
  //     selectedCard: val,
  //   });
  // };

  private bindVolOverlayIndex = (index: number) => {
    this.setState({
      volumeOverlayIndex: index,
    });
  };
  private bindScalpMaskIndex = (index: number) => {
    this.setState({
      volumeScalpMaskIndex: index,
    });
  };
  private loadColorMap = async () => {
    // @ts-ignore
    const { colors: aparcAsegVolumeColors, colorMap: aparcAsegVolumeBrentColorMap } = await setAparcVolumeColorMap();
    // @ts-ignore
    const { colors: a2009sAsegVolumeColors, colorMap: a2009sAsegVolumeBrentColorMap } = await setAparcS2009sVolumeColorMap();
    this.setState({
      aparcAsegVolumeBrentColorMap,
      aparcAsegVolumeColors,
      a2009sAsegVolumeBrentColorMap,
      a2009sAsegVolumeColors,
    });
    // 结构分区有36个，解析的colors数组长度应该是36
    const aparcAsegColorMapUrl1 = 'static/vendors/brainbrowser/colormap/surfAnatAparcAsegLH.txt';
    BrainBrowser.loader.loadColorMapFromURL(aparcAsegColorMapUrl1, async (colorMap: any) => {
      const { colors: surfaceColor36 } = await initColorMap(colorMap);
      if (surfaceColor36) {
        this.setState({ aparcAsegColors: surfaceColor36, aparcAsegBrentColorMap: colorMap });
      }
    });
    // 细粒度结构分区有76个，解析的colors数组长度应该是76
    const aparcAsegColorMapUrl2 = 'static/vendors/brainbrowser/colormap/SurfAnatAparcA2009sAsegLH.txt';
    BrainBrowser.loader.loadColorMapFromURL(aparcAsegColorMapUrl2, async (colorMap: any) => {
      const { colors: surfaceColor72 } = await initColorMap(colorMap);
      if (surfaceColor72) {
        this.setState({ aparcA2009sAsegColors: surfaceColor72, aparcA2009sAsegBrentColorMap: colorMap });
      }
    });
  };

  private viewerLoadColorMapFromURL = async (viewer: any, url: string) => {
    return new Promise((resolve: any) => {
      viewer.loadColorMapFromURL(url, () => {
        resolve();
      });
    });
  };

  private onChangeShowPatch = async (checked: boolean) => {
    const { surfaceViewer, selectedColorAtlaType } = this.state;
    if (surfaceViewer) {
      if (!checked) {
        await this.viewerLoadColorMapFromURL(surfaceViewer, `static/vendors/brainbrowser/colormap/${Colormap.White}`);
      } else {
        if (selectedColorAtlaType === 'apracA2009s') {
          await updateAparcA2009sAsegSurfaceColorMap(checked, this.state);
        } else if(selectedColorAtlaType === 'aprac') {
          await updateAparcAsegSurfaceColorMap(checked, this.state);
        }
      }
    }

    this.initViewerDrawSpot();
    if (!checked) {
      this.setState({
        selectedColorMapPatch: -1,
      });
    }
  };

  private onShowTargetPatch = async (patchId: number) => {
    const { selectedColorAtlaType } = this.state;
    this.setState({
      selectedColorMapPatch: patchId,
    });
    if (selectedColorAtlaType === 'apracA2009s') {
      await updateAparcA2009sAsegSurfaceColorMap(true, this.state, patchId, true);
    } else if(selectedColorAtlaType === 'aprac') {
      await updateAparcAsegSurfaceColorMap(true, this.state, patchId, true);
    }
  };

  private onChangeType = async (selectedColorAtlaType: string) => {
    let volumeArti = this.getVolumeOverlayArti(selectedColorAtlaType);
    let url = volumeArti.artiDef.colormap;
    // @ts-ignore
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const {colors, colorMap} = await getVolumeColorAndColorMap(url);
    this.setState({
      selectedColorAtlaType,
      selectedColorMapPatch: -1,
      colors,
      brentColorMap: colorMap,
      loading: true,
    }, async () => {
      // 切换后颜色涂的不对，原因在于生成的蒙层没有换，仅仅修改颜色无法匹配上。需要调用makeVolume修复volumeviewer，调用viewer.loadIntensityDataFromURL 修复surface图
      await this.onChangeShowPatch(true);
    });
  };
  // 编辑时，修改了靶点基础属性
  private onUpdateBaseField = (spot: SpotType, index: number) => {
    const { spotList } = this.state;
    // if((!spot.name || !spot.name.trim()) && spot.name !== '0'){
    //   spot.name = `${_.clone(spot.vertex || '')}`;
    // }
    spotList.splice(index, 1, spot);
    this.setState({
      spotList: spotList,
      isNotComplete: spotList.some(v => v.stimulateTemplate?.isNotComplete),
      isChanged: true,
    }, () =>{
      this.forceUpdate();
    });
  };
  // 编辑时，修改了靶点刺激参数
  private onUpdateTemplate = (template: StimulateTemplateModel, index: number) => {
    const { spotList } = this.state;
    let target = spotList[index];
    target.stimulateTemplate = template;
    spotList.splice(index, 1, target);
    this.setState({
      spotList: spotList,
      isNotComplete: spotList.some(v => v.stimulateTemplate?.isNotComplete) || !!template.isNotComplete,
      isChanged: true,
    }, () => {
      this.forceUpdate();
    });
  };

  // 渲染脑图色卡所需的颜色列表
  private getBrentColorMap = (selectedColorAtlaType: string) =>{
    const {
      aparcA2009sAsegBrentColorMap,
      aparcAsegBrentColorMap,
    } = this.state;

    switch (selectedColorAtlaType){
      case 'aprac':
        return aparcAsegBrentColorMap;
        break;
      case 'apracA2009s':
        return aparcA2009sAsegBrentColorMap;
        break;
      default:
        return aparcAsegBrentColorMap;
    }
  };
  // 渲染色卡，支持结构分区
  // 功能分区如果支持，需要打开文件获取
  private renderColorMap = () =>{
    const {
      selectedColorMapPatch,
      selectedColorAtlaType,
    } = this.state;
    const arti = getArtidef(selectedColorAtlaType);
    let patchNames: string[] = [];
    if(['aprac','apracA2009s'].includes(selectedColorAtlaType)){
      patchNames = arti.patchNames;
    }

    const brentColorMap = this.getBrentColorMap(selectedColorAtlaType);
    if (brentColorMap) {
      return <ColorAtla selected={selectedColorMapPatch}
        brentColorMap={brentColorMap}
        patchNames={patchNames}
        haveBold={false} // control 18 82 152 213 select option
        selectType={selectedColorAtlaType}
        onIconClick={this.resetColorMap}
        onChangeType={this.onChangeType}
        onChangePatch={this.onShowTargetPatch}/>;
    }else{
      return <></>;
    }
  };

  // 日志逻辑
  private onLog = async () => {
    this.setState({
      isShowLog: true,
    });
  };

  private onCloseLogModel = () => {
    this.setState({
      isShowLog: false,
    });
  };

  // 检查治疗天数限制
  private checkTreatmentDaysLimit = async (): Promise<void> => {
    const { projectId, patientId, planId } = this.state;
    if (!projectId || !patientId || !planId) return;

    try {
      await this.point200Api.checkTreatment(projectId, patientId, planId);
      this.setState({ treatmentDaysExceeded: false });
    } catch (e: any) {
      if (e?.code === 'PNT_PI10110') {
        this.setState({ treatmentDaysExceeded: true });
      }
    }
  };

  private onStart = async () => {
    const { intl } = this.props;
    if(!ISLOCATIONDEBUG){
      if (isKiosk && !tmsSocket?.beatType){
        // eslint-disable-next-line no-void
        // void message.warning(intl.formatMessage({id: 'point200.planPage.notFoundBat'}));

        return;
      }
    }
    const {
      projectId, planId, patientId, patient,
      planInfo, spotList, plans,
      volumeScalpMaskIndex,
      volumeViewer,
    } = this.state;
    await ValidateExpireModal(planInfo?.expireTime, () => {
      this.goBack();
    });
    let user = auth.currentUser();
    let userId = user?.id;
    if (!projectId || !patientId || !planId || !userId) return;
    let restingPlanIds = await getCuringListBasePatientOfNative(patientId);

    sessionStorage.setItem(`${planId}`, new Date().getTime().toString());
    if(checkOfflineLevel() === 'level3' && localStorage.getItem('network') === 'online'){
      this.setState({
        startLoading: true,
      });
      try{
        let allowStart = await sniffer({projectId, patientId, planId}, intl, this.point200Api, isKiosk);
        let recentPlan = await this.point200Api.getPlanById(projectId, patientId, planId);
        if (recentPlan.hasDeprecated) {
          this.jumptoHome();

          return;
        }
        if([PlanStatus200.Finished, PlanStatus200.Terminated].includes(recentPlan.planStatus)){
          allowStart = false;
          this.setState({
            planStatus: recentPlan.planStatus,
            plans: plans.map(item=>{
              if(item.id === recentPlan.id){
                item = recentPlan;
              }

              return item;
            }),
          }, () =>{
            this.forceUpdate();
          });
          const label1 = intl.formatMessage({id:'point.cureProject.terminalUser'}, {value: recentPlan.terminatedByUser?.username});
          const label2 = intl.formatMessage({id:'point.cureProject.terminalTime'}, {value: moment(recentPlan.terminatedAt).format('YYYY-MM-DD HH:mm:ss')});
          // eslint-disable-next-line no-void
          void message.warning({content: `${label1}${label2}`, key: 'terminalTooltip'});
        }
        if(ISLOCATIONDEBUG){
          allowStart = true;
        }
        if (!allowStart) {
          this.setState({
            startLoading: false,
          });

          return;
        }

        // 检查治疗天数是否已达上限
        try {
          await this.point200Api.checkTreatment(projectId, patientId, planId);
        } catch (e: any) {
          this.setState({
            startLoading: false,
          });
          if (e?.code === 'PNT_PI10110') {
            // eslint-disable-next-line no-void
            void message.warning('治疗天数已达上限，无法开始治疗');
          } else {
            // eslint-disable-next-line no-void
            void message.warning('治疗校验失败，请稍后重试');
          }
          return;
        }
      } catch (e: any) {
        SendSetHistoryLogs(`点击开始治疗跳转页面报错，错误信息:${(e || {}).stack}`);
      }
    }

    try {
      // webworker 开一个线程先计算 高亮的进入点；再开一个现场算其余点进入点。
      if(volumeScalpMaskIndex){
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        beyondAllEntryPoint(volumeViewer, volumeScalpMaskIndex, spotList.filter(v => v.isActive), planId, this.contextUrl);
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        beyondAllEntryPoint(volumeViewer, volumeScalpMaskIndex, spotList.filter(v => !v.isActive), planId, this.contextUrl);
      }
      // console.log(res)
      if (restingPlanIds.length && restingPlanIds.includes(planId)) {
        // 休息中，仍然可能切换靶点，故此时需要更新休息中的刺激点
        await setNativeResting(planId, projectId, patientId, spotList, { patientCode: patient?.patientCode!, treatmentCount: this.state.treatmentCount - this.state.remainTreatmentCount + 1 });
        // 休息中，并且是当前方案，此时点击 开始治疗不再发送 开始治疗日志
        await this.onJumpToTreatment();

        return;
      }else{
        await setNeedUpdateRemainTreatmentCount(planId, true);
        if(patient && planInfo){
          let gender =  patient.gender ? intl.formatMessage({ id: `subject.gender.${patient.gender}` }) : '未知';
          let age = getAge(moment(patient.dateOfBirth).format('YYYY-MM-DD'));
          const { treatLogId, patientInfo } = dataOfTreatLogPatient({ patientId, planId, planInfo, auth, gender, age, addTreatmentCount:1});
          // eslint-disable-next-line no-console
          console.log('treatLogId--------------', treatLogId);
          // eslint-disable-next-line no-console
          console.log('patientInfo--------------', patientInfo);
          await setTreatLogs(treatLogId, patientInfo, undefined);
        }
        // 开始治疗必定是台车端，需要进行日志的比对,推送日志到远端
        await pushNativeLogs(this.point200Api, this.state.planId);
        // 写入日志，有网发送远端，没网本机存储
        await this.createLog(EnumCreatePlanLogModelEvent.StartTreatment, planId);
        await this.onJumpToTreatment();
        await setNativeResting(planId, projectId, patientId, spotList, { patientCode: patient?.patientCode!, treatmentCount: this.state.treatmentCount - this.state.remainTreatmentCount + 1 });
      }
    } catch (error: any) {
      SendSetHistoryLogs(`点击开始治疗跳转页面报错,计算法线信息,错误信息:${(error || {}).stack}`);
    }
    this.setState({
      startLoading: false,
    });
  };

  // 编辑时，再次检查是否有网
  // 编辑时，必须保证haveT1=true; 包含有脑图
  private onEdit = () => {
    if(localStorage.getItem('network') === 'offline'){
      // eslint-disable-next-line no-void
      // void message.warning({id:'point200.offline.blockTooltip'});

      return;
    }
    if(!this.state.haveT1){
      // eslint-disable-next-line no-void
      void message.warning({id:'point200.offline.noT1AndNoEdit'});

      return;
    }
    this.onChangeShowPatchColorMap(false);
    this.handleLayout(true);
    this.setState({
      isEdit: true,
      isNotComplete: false,
      isChanged: false,
      spotList: this.state.spotList.map((item: SpotType) => {
        return {...item, isActive: false};
      }),
    }, () => {
      this.onClickEditSpot();
    });
  };

  // 请求靶点规划，跳转到上传页面
  private onAddUploadBold = () =>{
    const {projectId, patientId, planId} = this.state;
    history.push({
      pathname: `/point/upload/project/${projectId}/patient/${patientId}`,
      state: { onlyBold: true, planId },
    });
  };
  // 都是运动区靶点不能保存
  // 没有治疗数量 不能保存
  // 前后端数据不一致，保存前校准一遍刺激参数
  private onSave = async (isSaveAs = false, isReTreatment = false) => {
    const listValidate = await Promise.all(this.formList.map((v: any) => v?.current?.validateFields().catch((e: any) => false)));
    for (let i = 0; i < listValidate.length; i++) {
      // 校验未通过
      if (listValidate[i] === false) {
        const { spotList } = this.state;
        this.setState({
          spotList: spotList.map((v, index) => {
            v.isActive = i === index;

            return v;
          }),
        });

        return;
      }
    }
    const { projectId, patientId, planId, spotList, planInfo, surfaceViewer, leftVertexNumber18, tkras2ras } = this.state;
    if(!patientId || !projectId || !planId || !leftVertexNumber18 || !surfaceViewer || !tkras2ras || !planInfo) return;
    if(spotList.every((item: SpotType)=> item.isMotion)){
      // eslint-disable-next-line no-void
      void message.error(this.props.intl.formatMessage({id:'point.cureProject.saveErrorNoSpot'}));

      return;
    }

    let newTreatmentCount = Number(this.state.treatmentCount);
    const remainTreatmentCount = getRealRemainCount(planInfo.treatmentCount!, newTreatmentCount, this.state.remainTreatmentCount);
    const params = await setSaveParams({ planInfo, spotList, tkras2ras, leftVertexNumber18, surfaceViewer, treatmentCount: newTreatmentCount, remainTreatmentCount}, isSaveAs);

    let res: any;
    if (isSaveAs) {
      res = await this.point200Api.copyPlanInfo(projectId, patientId, planId, Object.assign({}, params, {createSource: EnumPlanModelCreateSource.ModifyPlan}) as CopyPlanModel);
      await this.createLog(EnumCreatePlanLogModelEvent.CreatePlan, res.id);
    } else {
      try{
        res = await this.point200Api.updatePlanInfo(projectId, patientId, planId, params);
      }catch (e) {
        // @ts-ignore
        if(e.code === 'SV33502'){
          setTimeout(async () =>{
            await this.initFetch(false, true);
            await this.fetchPlans();
          },1);
        }
      }
    }

    if (res.id) {
      if(isSaveAs){
        // eslint-disable-next-line no-void
        void notification.success({
          message: '',
          description: this.props.intl.formatMessage({ id: 'point.cureProject.copySuccessMessage' }),
          placement: 'bottomRight',
          duration: 2.5,
        });
      }
      await this.initPatient();
      await this.initFetch();
      await this.fetchPlans();

      return;
    }
  };

  // 取消编辑，重置到最开始状态
  private onCancelEdit = async (content?: string) => {
    this.validateData();

    this.setState({
      isEdit:false,
      // spotList: initSpotList(planInfo!.treatmentTargets, planInfo?.snapshotConfirmedTreatmentTargets),
      isChanged: false,
    }, async () =>{
      await this.initPatient();
      await this.initFetch();
      await this.fetchPlans();
    });
  };

  // 添加靶点，最多20；添加靶点后，需要立刻聚焦一次；将滚动条滚到最下面
  private onAddSpot = async () => {
    const { spotList } = this.state;
    const length = spotList.length;
    if (length >= 20) {
      // @ts-ignore
      // eslint-disable-next-line no-void
      void message.warning(this.props.intl.formatMessage({id:'point.cureProject.maxSpotNum'}));

      return;
    }
    const newSpotList: SpotType[] = spotList.map((item: SpotType) => {
      return { ...item, isActive: false };
    });

    this.validateData();
    addSkipNumber();
    const defaultName = this.props.intl.formatMessage({id: 'point.cureProject.spot.defaultName'});
    const newSpot: SpotType = Object.assign({}, { ...CommonSpot, isActive: true, uniqueId: uuidv4(), isNew: true,stimulateTemplate: { isNotComplete: true} }, { color: colors[length], name: `${defaultName}${length + 1}` }) as any as SpotType;
    // @ts-ignore
    this.onFocusMarkInViewer(newSpot);
    this.setState({
      spotList: [...newSpotList, newSpot],
      isNotComplete: true,
      isChanged: true,
    },() =>{
      this.spotListWrap.current.scrollTop = (length + 1) * 400;
    });
  };

  // 删除临时靶点
  private onDeleteSpot = async (spot: SpotType, index: number) => {
    let { spotList } = this.state;
    // spotList.splice(index, 1);
    const newSpotList = spotList.filter((v, i) => i !== index);
    await this.onSaveCard(undefined, newSpotList);
    this.formList[index] = undefined;

    this.setState({
      spotList: newSpotList,
      isNotComplete: spotList.some(v => v.stimulateTemplate?.isNotComplete),
      isChanged: false,
    }, () => {
      this.forceUpdate();
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
    });
  };

  private onJumpToMeasureScope = async () =>{
    const {patientId, planId, projectId, planInfo, spotList, volumeScalpMaskIndex, volumeViewer} = this.state;
    if(!patientId || !planId || !projectId || !planInfo || !spotList) return;
    // webworker 开一个线程先计算 高亮的进入点；再开一个现场算其余点进入点。
    if(volumeScalpMaskIndex){
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      beyondAllEntryPoint(volumeViewer, volumeScalpMaskIndex, spotList.filter(v => v.isActive), planId, this.contextUrl);
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      beyondAllEntryPoint(volumeViewer, volumeScalpMaskIndex, spotList.filter(v => !v.isActive), planId, this.contextUrl);
    }
    await jumpToEmgTmsPage({patientId, planId, projectId, planInfo});
    await setMotionSpotOfNative({id: planId, obj: spotList.find(item => item.isActive && item.isMotion) as SpotType});
  };

  private onJumpToTreatment = async () => {
    // const {intl} = this.props;
    if(!ISLOCATIONDEBUG){
      if (isKiosk && !tmsSocket?.beatType){
        // eslint-disable-next-line no-void
        // void message.warning(intl.formatMessage({id: 'point200.planPage.notFoundBat'}));

        return;
      }
    }
    const { projectId, patientId, planId, planInfo, patient } = this.state;
    if(!projectId || !planId || !patientId || !planInfo || !patient) return;
    await jumpToTreatment({projectId, planId, patientId, patient, planInfo});
  };

  // 脑图颜色重置
  private resetColorMap = async () => {
    this.setState({
      selectedColorMapPatch: -1,
    });
    await this.onChangeShowPatch(true);
  };

  // 200 医生端，拒绝确认
  private onRefuse = async () => {
    history.goBack();
  };
  // 200 医生端，医生确认
  // 33502 表示被其他人确认过了，获取新的
  private onEnsure = async () => {
    const { projectId, patientId, planId, treatmentCount } = this.state;

    const params = {
      treatmentCount,
    };

    try{
      let res = await this.point200Api.confirmPlanInfo(projectId!, patientId!, planId!, params);
      if (res.id) {
        await this.processData(res);
        await this.fetchPlans();
      }
    }catch (e) {
      // @ts-ignore
      if(e.code === 'SV33502'){
        setTimeout(async () =>{
          await this.initFetch(false, true);
          await this.fetchPlans();
        },1);
      }
    }
  };

  // 初始化时，预先加载需要渲染脑图所需的文件。主要是pialurl文件是必须的。
  // 同时备份本机数据，如果没有网络，从备份中获取
  // havebold 在200的当前需求下 永远为false，bold文件用于渲染，功能分区  18  92  156  213脑图。
  private downloadFiles = async (outputFiles: OutputFile[] | undefined, haveBold: boolean) => {
    if (!outputFiles || !outputFiles.length) return;
    const { projectId, patientId, planId } = this.state;
    if(!projectId || !patientId || !planId) return;

    let surfaceList = await getFileListSurface(outputFiles, this.point200Api, projectId, patientId, planId);
    let pialUrl = surfaceList.find((item: SurfaceFileType) => item.descriptor === 'pial-surface-gifti')?.signedUrl;
    const convex = outputFiles.find(v => v.logicalName === 'head_convex_hull.ply');
    if (convex) {
      try {
        this.contextUrl = (await this.point200Api.getDownloadFileUrl([{ storagePath: convex.s3MainKey }]))[0].signedUrl!;
      } catch (error) {
        //
      }
    }
    if(!pialUrl){
      // eslint-disable-next-line no-void
      void message.error(this.props.intl.formatMessage({id:'point200.offline.noPialUrl'}));

      this.setState({
        loading: false,
        surfaceLoading: false,
        volumeLoading: false,
      });

      return;
    }
    let scalpMaskUrl = surfaceList.find((item: SurfaceFileType) => item.descriptor === 'scalp-mask-surface')?.signedUrl;
    let aparcAsegSurfaceIntensityDataUrl = surfaceList.find((item: SurfaceFileType) => item.descriptor === 'anat-aparc-wb-surf-txt')?.signedUrl;
    let aparcA2009sAsegSurfaceIntensityDataUrl = surfaceList.find((item: SurfaceFileType) => item.descriptor === 'anat-a2009s-wb-surf-txt')?.signedUrl;
    this.setState({
      pialUrl,
      scalpMaskUrl,
      aparcAsegSurfaceIntensityDataUrl,
      aparcA2009sAsegSurfaceIntensityDataUrl,
    });
    if (checkOfflineLevel() === 'level3' || ISTESTNEXT) {
      let params = surfaceList.map(item => {
        return {
          departmentId: projectId,
          subjectId: patientId,
          planId: planId,
          fileId: item.fileId,
          descriptor: item.descriptor,
          signedUrl: item.signedUrl!,
          file: item,
        };
      });
      await setFileOfNative(params);
    }
    let volumeFiles = await getFileListVolume(outputFiles, this.point200Api, projectId, patientId, planId);
    this.setState({
      vizArray: volumeFiles,
      aparcVolumeOverlayAtri: volumeFiles.find((item: VolumeFileType) => item.descriptor === 'anat-aparc-aseg-vol'),
      a2009sVolumeOverlayAtri: volumeFiles.find((item: VolumeFileType) => item.descriptor === 'anat-a2009s-aseg-vol' || item.descriptor === 'anat-a2009s-wb-surf-txt'),
    });
    if (checkOfflineLevel() === 'level3' || ISTESTNEXT) {
      let params = volumeFiles.map(item => {
        return {
          departmentId: projectId,
          subjectId: patientId,
          planId: planId,
          fileId: item.fileId,
          descriptor: item.descriptor,
          signedUrl: item.signedUrl!,
          file: item,
        };
      });
      await setFileOfNative(params);
    }

    if(checkOfflineLevel() === 'level2') return;
    // 获取脑图计算的必要文件, 脱网不编辑，无许thrakras
    const tkras2ras = await getTkras2ras(outputFiles, this.point200Api, projectId, patientId, planId);
    if(!tkras2ras) {
      // eslint-disable-next-line no-void
      void message.warning(this.props.intl.formatMessage({ id: 'point200.offline.brainTkras2rasFileError' }));

      return;
    }
    this.setState({ tkras2ras, loadedTkras2ras: true });
    if (checkOfflineLevel() === 'level3' || ISTESTNEXT) {
      await setPlanTkras2rasOfNative(this.state.planId!, tkras2ras);
    }

    // 获取左脑顶角号，用于计算hemi
    let leftVertexNumber18 = await getLeftBrainDotLength(outputFiles, this.point200Api, projectId, patientId, planId);
    await setLeftVertexNumberOfNative(planId, leftVertexNumber18);
    this.setState({
      leftVertexNumber18,
    });
  };

  // 显示脑图颜色是，surface渲染需要的颜色文件。也可以获取volume渲染的分区文件
  private getVolumeOverlayArti = (selectedType: string) =>{
    const { a2009sVolumeOverlayAtri, aparcVolumeOverlayAtri } = this.state;
    const tempMap = {
      'apracA2009s': a2009sVolumeOverlayAtri,
      'aprac': aparcVolumeOverlayAtri,
    };
    if(checkOfflineLevel() === 'level2') return tempMap.aprac;

    return tempMap[selectedType];
  };
  // 获取脑图render需要的覆盖文件
  private getVolumeOverlayArtiForRender = (selectedType: string) =>{
    const { a2009sVolumeOverlayAtri, aparcVolumeOverlayAtri } = this.state;
    const tempMap = {
      'apracA2009s': a2009sVolumeOverlayAtri,
      'aprac': aparcVolumeOverlayAtri,
    };
    if(checkOfflineLevel() === 'level2') return tempMap.aprac;

    return tempMap[selectedType];
  };

  // 点击开始治疗时 发送日志
  private createLog = async (event: EnumCreatePlanLogModelEvent, planId: number) =>{
    const {treatmentCount, remainTreatmentCount, projectId, patientId} = this.state;
    await createLogEvent(event, {planId, projectId, patientId, treatmentCount, remainTreatmentCount}, this.point200Api);
  };

  // 初始化时，切换视图时，给脑图绘制靶点
  private initViewerDrawSpot = () => {
    const { volumeLoading, surfaceLoading, surfaceViewer, volumeViewer } = this.state;
    if (volumeLoading || surfaceLoading || !surfaceViewer || !volumeViewer) return;
    const { spotList } = this.state;
    if (this.updateFirstDotTimer) {
      clearTimeout(this.updateFirstDotTimer);
      this.updateFirstDotTimer = undefined;
    }
    if(!spotList.length){
      this.updateFirstDotTimer = setTimeout(async () => {
        updateVolumeCursorByWorld(volumeViewer, CommonSpot.volSeed);
        clearSurfaceDot(surfaceViewer);
        surfaceViewer.setView('lateral');
        surfaceViewer.update = true;
        this.setState({
          loading: false,
        });
      }, 400);

      return;
    }
    let selectedSpot = spotList.find((v: SpotType) => v.isActive);

    if (!selectedSpot) return;
    let color = selectedSpot.color ? parseInt(selectedSpot.color.replace('#', '0x'), 16) : 0x0074D9;
    this.updateFirstDotTimer = setTimeout(async () => {
      // @ts-ignore
      this.onlyUpdateViewMarker(selectedSpot.volSeed, selectedSpot.surfSeed, color, 'spot');
      surfaceViewer.zoom = .80;
      this.setState({
        loading: false,
      });
      clearTimeout(this.updateFirstDotTimer);
      this.updateFirstDotTimer = undefined;
    }, 800);
  };
  private onImportPlan = async (path?: string) => {
    if (!path) return;
    try {
      let spotList = await readJsonFile(path);
      console.log('导入的数据', spotList); // eslint-disable-line no-console
      if (Array.isArray(spotList) && spotList.length > 0) {
        this.setState({ spotList, isNotComplete: false });
      }
    } catch (error) {
      //
    } finally {
      this.setState({
        fileModalVisabled: false,
      });
    }
  };
}

// @ts-ignore
const mapStateToProps = (state: DesktopApplicationState) => {
  return {
    pageData: state.pageData,
    fileTransfer: state.fileTransfer,
  };
};

export const CureContainer = connect(mapStateToProps)(injectIntl(IntlCureContainer));

