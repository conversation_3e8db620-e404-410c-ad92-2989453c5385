import React from 'react';
import lodash from 'lodash';
import { injectIntl } from 'react-intl';
import { DiseaseFilterPage } from './diseaseFilterPage';
import { IntlPropType } from '@device/pnt-component/packages/lib/propTypes';
import { DraggableControl } from '@device/pnt-component/packages/ui-component/visualizers/control/draggableControl';
import { CardHeader } from './cardHeader';
import { CureNum } from './cureNum';
import { TmsPreviewFooter } from './tmsPreviewFooter';
import {
  PlanInfoModel,
  PlanModel,
  StimulateTemplateModel,
  StimulateType,
  SubjectModel,
} from '@/common/types/index.defs';
import { SpotType } from '@/renderer/utils/pageType';
import { PlanStatus200 } from '@/renderer/constant/planStatus200';
import { transverByDb } from '../../../../renderer/utils/calExciteParam';
import styles from './tmsPreviewControl.less';
import { NavViewType } from '../../../component/treatmentViewTabs/treatmentViewTabsUtils';
import { BatAngleType, BatTranslationType } from '../../../component/pointSurfaceViewer/pointSurfaceViewerUtils';
import { EditCurePageOfTms } from './editCurePageOfTms';

type tmsPreviewCurePagePropType = IntlPropType & {
  navViewType: NavViewType;
  isTracing: boolean;
  batTranslation?: BatTranslationType;
  batAngle?: BatAngleType;
  spotList: SpotType[];
  tMSState: boolean;
  planInfo: PlanInfoModel;
  patient: SubjectModel;
  children: React.ReactElement;
  beatTemperature: number;
  coolingIsError: boolean;
  isTmsClose: boolean;
  treatedCount: number;
  absoluteStrength: number;
  btnStatus: boolean;
  planTarget: PlanModel;
  btnDisabled: boolean;
  onChangeActive(index: number): void;
  onStartCure(): void;
  onOver(): void;
  onRegister(): void;
  onResting(): void;
  onEnsure(spotList: SpotType[]): void;
  updateAbsoluteStrength(absoluteStrength: number): void;
  isControl?: boolean;
  showLogs(): void;
};
type tmsPreviewCurePageStateType = {
  selectedCard: string;
  spotList: SpotType[];
  isNotComplete: boolean;
};

export class InnerTmsPreviewControl extends React.Component<tmsPreviewCurePagePropType, tmsPreviewCurePageStateType> {
  constructor(props: tmsPreviewCurePagePropType) {
    super(props);
    this.state = {
      selectedCard: 'cureResult',
      spotList: props.spotList,
      isNotComplete: false,
    };
  }
  public shouldComponentUpdate(prepProps: tmsPreviewCurePagePropType, prepState: tmsPreviewCurePageStateType): boolean{
    if (prepState.selectedCard !== this.state.selectedCard) {
      return true;
    }

    if (prepProps.tMSState !== this.props.tMSState) {
      return true;
    }
    if (prepProps.btnDisabled !== this.props.btnDisabled) {
      return true;
    }

    if (prepProps.beatTemperature !== this.props.beatTemperature) {
      return true;
    }

    if (prepProps.coolingIsError !== this.props.coolingIsError) {
      return true;
    }

    if (prepProps.treatedCount !== this.props.treatedCount) {
      return true;
    }

    if (prepProps.isTmsClose !== this.props.isTmsClose) {
      return true;
    }

    if (prepProps.btnStatus !== this.props.btnStatus) {
      return true;
    }

    if (prepProps.absoluteStrength !== this.props.absoluteStrength) {
      return true;
    }

    if (prepState.isNotComplete !== this.state.isNotComplete) {
      return true;
    }

    return !lodash.eq(prepProps.spotList, this.props.spotList);
  }

  public componentDidMount(): void {
    const spot = this.props.spotList.find((obj: SpotType) => obj.isActive);
    if (spot && spot.stimulateTemplate?.strength) {
      this.props.updateAbsoluteStrength(spot.stimulateTemplate?.strength);
    }
  }

  public render() {
    const {selectedCard, spotList} = this.state;
    const {planInfo, patient ,absoluteStrength, planTarget} = this.props;

    return <DraggableControl
      title="View Control"
      isNarrow
      style={{ width: '520px' }}
      isShowTitle={false}
      footer={this.renderControlFooter}
    >
      <div className={styles.content_container}>
        <div className={styles.right_header_container}>
          {this.renderCardHeader()}
        </div>
        <div className={styles.spot_list_area}>
          {selectedCard === 'cureResult' && this.props.children}
          {selectedCard === 'cureResult' &&  this.renderCureNum()}
          {/* TMS治疗预览，仅可以编辑刺激参数，不可以编辑名称，靶点位置等*/}
          {selectedCard === 'cureResult' && <EditCurePageOfTms
            spotList={spotList}
            onClickSpot={this.onClickSpot}
            onUpdateTemplate={this.onUpdateTemplate}
            patient={patient}
            absoluteStrength={absoluteStrength}
            planStatus={PlanStatus200.Usable}
            isNotComplete={this.state.isNotComplete}
            displayItem={{
              stimulateType: true,
              plexusInnerFrequency: true,
              plexusInterFrequency: true,
              plexusInnerPulseCount: true,
              plexusCount: true,
              strandPulseCount: true,
              intermissionTime: true,
              strandPulseFrequency: true,
              innerStrandPulseCount: true,
              headIcon: true,
            }}
          />}
          {selectedCard === 'diseaseFilter' && <DiseaseFilterPage planInfo={planInfo} productType={planTarget.productType}/>}
        </div>
      </div>
    </DraggableControl>;
  }

  private renderCureNum = () =>{
    const {treatmentCount, remainTreatmentCount, planned_treatment_days, completed_treatment_days} = this.props.planInfo;

    return <div style={{borderTop: '1px solid #00000026'}}>
      <CureNum
        isEdit={false}
        treatmentCount={treatmentCount!}
        remainTreatmentCount={remainTreatmentCount!}
        plannedTreatmentDays={planned_treatment_days}
        completedTreatmentDays={completed_treatment_days}
        onRef={() => undefined}
      />
    </div>;
  };

  private renderCardHeader = () =>{
    const {intl} = this.props;
    let options = [
      { value: 'cureResult', text: intl.formatMessage({ id: 'point.audit.card.cureResult' }) },
      { value: 'diseaseFilter', text: intl.formatMessage({ id: 'point.audit.card.diseaseFilter' }) },
    ];
    const { selectedCard } = this.state;

    return <CardHeader options={options} selected={selectedCard} onChangeCard={this.onChangeCard}/>;
  };

  private onChangeCard = (val: string) => {
    this.setState({
      selectedCard: val,
    },() =>{
      this.forceUpdate();
    });
  };

  private renderControlFooter = () =>{
    const { isTracing, batTranslation, batAngle, navViewType, patient, tMSState, btnStatus, spotList, btnDisabled} = this.props;
    if (this.state.selectedCard === 'diseaseFilter' || !patient) return <></>;
    let disabledStart = !tMSState;
    if(!patient.motionThreshold){
      disabledStart = true;
    }
    const spot = spotList.find((item: SpotType) => item.isActive);

    return <TmsPreviewFooter showLogs={this.props.showLogs} isControl={this.props.isControl} isNotComplete={this.state.isNotComplete} spot={spot as SpotType} btnDisabled={btnDisabled} onEnsure={this.onEnsure} btnStatus={btnStatus} isTracing={isTracing} batTranslation={batTranslation} batAngle={batAngle} navViewType={navViewType} onOver={this.onOver} onStart={this.onStart} onRegister={this.onRegister} onResting={this.onResting} disableStart={disabledStart}/>;
  };

  private onOver = () =>{
    this.props.onOver();
  };

  private onStart = () =>{
    this.props.onStartCure();
  };

  private onRegister = () =>{
    this.props.onRegister();
  };
  private onResting = () =>{
    this.props.onResting();
  };

  private onClickSpot = (index: number) => {
    const spotList = lodash.cloneDeep(this.state.spotList);
    spotList.map((obj: SpotType, objindex: number) => obj.isActive = (objindex === index));
    this.setState({ spotList });
    this.props.onChangeActive(index);
  };

  private onUpdateTemplate = (template: StimulateTemplateModel, index: number) =>{
    // 更新父级spot，不更新图
    const {spotList, updateAbsoluteStrength } = this.props;
    let target = spotList[index];
    if (!template.pulseTotal || !template.treatmentTime) {
      const { c, d, e, T5, T3 } = transverByDb(template);
      const isNormal = template.stimulateType === StimulateType.Normal;
      let N = isNormal ? c * e : c * d * e;
      template.pulseTotal = N;
      let treatmentTime = isNormal ? T5  : typeof(T3) === 'string'? parseFloat(T3) : T3 ;
      template.treatmentTime = treatmentTime;
    }
    target.stimulateTemplate = template;
    // target.isActive = true;
    spotList.splice(index, 1, target);
    this.setState({
      spotList: lodash.cloneDeep(spotList),
      isNotComplete: spotList.some(v => v.stimulateTemplate?.isNotComplete),
    }, () => {
      this.forceUpdate();
    });
    updateAbsoluteStrength(template.strength);
  };

  private onEnsure = () => {
    const { onEnsure} = this.props;
    onEnsure(this.state.spotList);
  };
}

export const TmsPreviewControl =  injectIntl(InnerTmsPreviewControl);
