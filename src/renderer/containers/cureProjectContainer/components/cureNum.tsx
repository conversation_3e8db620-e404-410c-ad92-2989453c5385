import React from 'react';
import { injectIntl } from 'react-intl';
import { IntlPropType } from '@device/pnt-component/packages/lib/propTypes';
import styles from './cureNum.less';
import { getProductionLine } from '@point/utils/getProductionLine';
import classnames from 'classnames';
type cureNumPropType = IntlPropType & {
  treatmentCount: number;
  remainTreatmentCount: number;
  isEdit: boolean;
  onRef(ref: any): void;
  plannedTreatmentDays?: number;
  completedTreatmentDays?: number;
};
type cureNumStateType = {};

export class InnerCureNum extends React.Component<cureNumPropType, cureNumStateType> {
  constructor(props: cureNumPropType) {
    super(props);
    this.state = {};
  }

  public componentDidMount() {
    this.props.onRef(this);
  }

  public render() {
    const { treatmentCount, isEdit, remainTreatmentCount, plannedTreatmentDays, completedTreatmentDays } = this.props;
    let firstLabel = treatmentCount - remainTreatmentCount;
    let label = `${firstLabel}`;
    const { isKiosk } = getProductionLine();

    // 构建显示文本
    let displayText = `已治疗${label}次`;

    // 如果有治疗天数数据，则添加天数显示
    if (plannedTreatmentDays !== undefined && completedTreatmentDays !== undefined) {
      displayText += `，已治疗${completedTreatmentDays}/${plannedTreatmentDays}天`;
    }

    return <>
      {!isEdit && <div className={classnames(styles.preview_num, !isKiosk ? styles.preview_num_middle : '')}>
        <span className={styles.label}>
          {/* {intl.formatMessage({ id: 'point.cureProject.treatmentForm.count' })} */}
          {displayText}
        </span>
      </div>}
    </>;
  }
}

export const CureNum = injectIntl(InnerCureNum);
