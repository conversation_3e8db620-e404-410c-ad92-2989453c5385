## 需求
- 方案详情页，任务列表，关于天数的展示，在原本已治疗次数的后面，添加“已治疗n/n天“
- 举例“已治疗2次，已治疗5/10天”
- 老数据没有这个字段就不展示了

- 计数规则，以每日00:00:00为开始，在刺激预览页点击“开始治疗”之后，按动线圈刺激按键，有过刺激，已治疗天数+1（测量运动阈值的单刺激，不加天数）
- 在页面“开始治疗”按钮上，添加限制，置灰不可点击

- 问题，NG文件可以往台车里面导入多次，可以修改患者ID等信息，实则无法有效控制治疗天数
- 每个NG要有唯一标识，已经导入到台车里面，就不允许再次导入了。弹窗提示，“已存在该文件，无法再次导入”
- 目前只考虑，单独台车情况


## 服务端接口变化
PlanModel 下新增治疗天数相关字段

{
    "plan_info": {
        "planned_treatment_days": 10,    // 预计治疗天数
        "completed_treatment_days": 7,    // 已治疗天数
        ...
    }
}
新增接口：校验是否可以治疗

POST /api/departments/1/subjects/{subjectId}/plans/{planId}/checktreatment
- 可以开始治疗返回 httpstatus200 和字符串 "true"
- 治疗天数已用尽返回通用错误结构，错误码 PNT_PI10110治疗天数已达上限。其他异常也返回通用错误结构
ng文件上传 / 预览 / 导入接口新增错误码： PNT_PI10109已导入过同一份NG文件

## 要求
-  不需要修复现有的ts问题,专注于本次功能的开发
